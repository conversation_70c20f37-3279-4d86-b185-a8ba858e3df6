# Experiment Configuration
name: "trading_ppo"
description: "Training PPO agent for crypto trading with TCN-CNN architecture"
tags: ["trading", "reinforcement_learning", "crypto"]
notes: |
  Default configuration for training a PPO agent with TCN-CNN architecture
  on cryptocurrency trading data.

# Project Structure
project_dir: "."
data_dir: "${project_dir}/data"
model_dir: "${project_dir}/models"
log_dir: "${project_dir}/logs"

# Data Configuration
data:
  data_dir: "${data_dir}"
  train_ratio: 0.8
  val_ratio: 0.1
  test_ratio: 0.1
  seq_len: 16
  batch_size: 128
  num_workers: 4
  pin_memory: true
  
  # Data augmentation
  use_augmentation: true
  aug_prob: 0.3
  
  # Feature scaling
  scale_features: true
  scale_method: "minmax"
  
  # Technical indicators
  indicators:
    - sma_10
    - sma_50
    - sma_200
    - rsi_14
    - bb_20_2
    - atr_14
    - macd_12_26_9
  
  # Data sources
  symbols:
    - BTC/USDT
    - ETH/USDT
  
  timeframes:
    - 1h

# Model Configuration
model:
  obs_dim: 32  # Will be set based on data
  action_dim: 3  # Buy, Sell, Hold
  seq_len: 16
  
  # TCN parameters
  tcn_channels: [64, 64, 64]
  tcn_kernel_size: 3
  tcn_dropout: 0.2
  
  # CNN parameters
  cnn_filters: [32, 64, 128]
  cnn_kernel_sizes: [3, 3, 3]
  cnn_strides: [1, 1, 1]
  cnn_dropout: 0.2
  
  # Output heads
  policy_hidden_dims: [128, 64]
  value_hidden_dims: [128, 64]
  
  # Initialization
  init_type: "xavier"
  init_gain: 0.02

# Training Configuration
training:
  # Training parameters
  num_episodes: 1000
  max_steps_per_episode: 1000
  batch_size: 128
  gamma: 0.99
  gae_lambda: 0.95
  clip_eps: 0.2
  entropy_coef: 0.01
  value_coef: 0.5
  max_grad_norm: 0.5
  
  # Optimization
  lr: 3e-4
  weight_decay: 1e-5
  lr_scheduler: "cosine"
  warmup_steps: 1000
  
  # Validation and checkpointing
  val_freq: 10
  save_freq: 50
  load_checkpoint: false
  checkpoint_path: ""
  
  # Random seed
  seed: 42

# Environment Configuration
env:
  # Trading parameters
  initial_balance: 10000.0
  commission: 0.001  # 0.1% commission per trade
  max_position: 1.0  # Max position size as fraction of account
  
  # Reward shaping
  reward_scale: 1.0
  use_pnl: true
  use_sharpe: true
  use_drawdown: false
  
  # Risk management
  stop_loss: 0.05  # 5% stop loss
  take_profit: 0.1  # 10% take profit
  max_drawdown: 0.2  # 20% max drawdown

# Logging Configuration
logging:
  log_dir: "${log_dir}"
  use_tensorboard: true
  log_level: "INFO"
  save_metrics: true
  save_freq: 10
  
  # Visualization
  plot_freq: 50
  num_episodes_to_plot: 10
