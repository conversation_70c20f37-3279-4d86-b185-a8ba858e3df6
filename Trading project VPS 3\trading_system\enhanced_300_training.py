"""
Enhanced Training Pipeline for $300 Capital
Optimized for smaller account sizes with realistic expectations
"""
import asyncio
import sys
import numpy as np
import torch
import torch.nn as nn
from datetime import datetime, timedelta, timezone
from pathlib import Path
from typing import Dict, List
import logging
import json

# Fix for Windows event loop issue
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# Add src to path
sys.path.append(str(Path(__file__).parent))

from src.data.binance_fetcher import BinanceDataFetcher
from src.trading.environment import GridTradingEnv, Action
from src.ml.integrated_training import TradingFeatureExtractor, SimpleTCNPPO

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class Enhanced300Training:
    """Enhanced training pipeline optimized for $300 starting capital."""

    def __init__(self):
        self.initial_capital = 300.0
        self.feature_extractor = TradingFeatureExtractor(lookback_window=24)
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        # Initialize model
        feature_dim = self.feature_extractor.get_feature_dim()
        self.model = SimpleTCNPPO(feature_dim, hidden_dim=64).to(self.device)  # Smaller model
        self.optimizer = torch.optim.Adam(self.model.parameters(), lr=1e-4)  # Lower learning rate

        # Training parameters optimized for small capital
        self.risk_per_trade = 0.05  # 5% risk
        self.grid_spacing = 0.0025  # 0.25%
        self.take_profit_multiplier = 2.0  # 2:1 R:R
        self.fee_rate = 0.001  # 0.1%

        logger.info(f"Initialized for $300 capital with {feature_dim} features on {self.device}")

    async def collect_diverse_data(self, days: int = 60) -> List:
        """Collect diverse market data including different market conditions."""
        logger.info(f"Collecting {days} days of diverse market data...")

        async with BinanceDataFetcher() as fetcher:
            end_time = datetime.now(timezone.utc)
            start_time = end_time - timedelta(days=days)

            response = await fetcher.fetch_historical_data(
                symbol="BTCUSDT",
                interval="1h",
                start_time=start_time,
                end_time=end_time
            )

            if not response.success:
                raise RuntimeError(f"Failed to fetch data: {response.error}")

            candles = response.data
            logger.info(f"Collected {len(candles)} candles")

            # Analyze market conditions
            prices = [c.close for c in candles]
            volatility = np.std(prices) / np.mean(prices)
            trend = (prices[-1] - prices[0]) / prices[0]

            logger.info(f"Market analysis: Volatility={volatility:.4f}, Trend={trend:.4f}")

            return candles

    def create_realistic_scenarios(self, candles: List) -> List[List]:
        """Create realistic trading scenarios for training."""
        scenarios = []

        # Split data into chunks for different market conditions
        chunk_size = len(candles) // 4

        for i in range(4):
            start_idx = i * chunk_size
            end_idx = start_idx + chunk_size if i < 3 else len(candles)
            scenario = candles[start_idx:end_idx]

            if len(scenario) > self.feature_extractor.lookback_window:
                scenarios.append(scenario)

        logger.info(f"Created {len(scenarios)} training scenarios")
        return scenarios

    def simulate_realistic_episode(self, candles: List) -> Dict:
        """Simulate trading with realistic constraints for $300 capital."""
        env = GridTradingEnv(
            initial_balance=self.initial_capital,
            risk_per_trade=self.risk_per_trade,
            grid_spacing=self.grid_spacing,
            take_profit_multiplier=self.take_profit_multiplier,
            fee_rate=self.fee_rate
        )

        env.reset(candles[0].close, candles[0].timestamp)

        episode_data = {
            'states': [],
            'actions': [],
            'rewards': [],
            'log_probs': [],
            'values': [],
            'balance_history': [self.initial_capital]
        }

        total_reward = 0
        trades_made = 0
        max_trades = 10  # Limit trades for $300 capital

        for i in range(self.feature_extractor.lookback_window, len(candles)):
            if trades_made >= max_trades:
                break

            try:
                # Extract features
                features = self.feature_extractor.extract_features(candles[:i+1])
                state_tensor = torch.FloatTensor(features).unsqueeze(0).to(self.device)

                # Get action from model
                action_idx, log_prob, value = self.model.act(state_tensor)

                # Convert to environment action
                action_map = {0: Action.BUY, 1: Action.SELL, 2: Action.HOLD}
                action = action_map[action_idx]

                # Execute step
                candle = candles[i]
                prev_balance = env.balance
                _, _, _, _ = env.step(action, candle.close, candle.timestamp, candle)

                # Calculate reward based on balance change and risk management
                balance_change = env.balance - prev_balance

                # Reward shaping for $300 capital
                if balance_change > 0:
                    reward = balance_change / 15.0  # Normalize by risk amount ($15)
                elif balance_change < 0:
                    reward = balance_change / 15.0  # Penalty for losses
                else:
                    reward = 0.01 if action == Action.HOLD else -0.001  # Small reward for holding, penalty for unnecessary actions

                # Bonus for maintaining capital
                if env.balance >= self.initial_capital * 0.95:  # Within 5% of starting capital
                    reward += 0.001

                # Store episode data
                episode_data['states'].append(features)
                episode_data['actions'].append(action_idx)
                episode_data['rewards'].append(reward)
                episode_data['log_probs'].append(log_prob)
                episode_data['values'].append(value)
                episode_data['balance_history'].append(env.balance)

                total_reward += reward

                if action != Action.HOLD:
                    trades_made += 1

            except ValueError:
                continue

        # Final metrics
        final_balance = env.balance
        total_return = (final_balance / self.initial_capital - 1) * 100

        episode_data.update({
            'total_reward': total_reward,
            'final_balance': final_balance,
            'total_return': total_return,
            'trades_made': trades_made,
            'max_drawdown': min(episode_data['balance_history']) / self.initial_capital - 1
        })

        return episode_data

    async def enhanced_training(self, episodes: int = 100) -> Dict:
        """Enhanced training with realistic scenarios."""
        logger.info(f"Starting enhanced training for {episodes} episodes...")

        # Collect diverse training data
        training_data = await self.collect_diverse_data(days=90)
        scenarios = self.create_realistic_scenarios(training_data)

        best_performance = {
            'reward': float('-inf'),
            'balance': 0,
            'return': -100,
            'episode': 0
        }

        training_history = []

        for episode in range(episodes):
            # Select random scenario
            scenario_idx = np.random.randint(0, len(scenarios))
            scenario = scenarios[scenario_idx]

            # Simulate episode
            episode_data = self.simulate_realistic_episode(scenario)

            if not episode_data['states']:
                continue

            # Calculate returns and advantages
            rewards = episode_data['rewards']
            values = episode_data['values']

            returns = []
            running_return = 0
            gamma = 0.99

            for reward in reversed(rewards):
                running_return = reward + gamma * running_return
                returns.insert(0, running_return)

            returns = torch.FloatTensor(returns).to(self.device)
            values = torch.FloatTensor(values).to(self.device)
            advantages = returns - values

            # Normalize advantages
            if len(advantages) > 1:
                advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)

            # Update model
            states = torch.FloatTensor(np.array(episode_data['states'])).to(self.device)
            actions = torch.LongTensor(episode_data['actions']).to(self.device)
            old_log_probs = torch.FloatTensor(episode_data['log_probs']).to(self.device)

            # Forward pass
            logits, new_values = self.model(states)
            probs = torch.softmax(logits, dim=-1)
            dist = torch.distributions.Categorical(probs)
            new_log_probs = dist.log_prob(actions)
            entropy = dist.entropy()

            # PPO loss with conservative clipping for small capital
            ratio = (new_log_probs - old_log_probs).exp()
            surr1 = ratio * advantages
            surr2 = torch.clamp(ratio, 0.9, 1.1) * advantages  # Tighter clipping
            policy_loss = -torch.min(surr1, surr2).mean()

            value_loss = nn.MSELoss()(new_values, returns)
            entropy_loss = -entropy.mean()

            total_loss = policy_loss + 0.5 * value_loss + 0.02 * entropy_loss

            # Backward pass
            self.optimizer.zero_grad()
            total_loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), 0.5)
            self.optimizer.step()

            # Track performance
            performance = {
                'episode': episode,
                'total_reward': episode_data['total_reward'],
                'final_balance': episode_data['final_balance'],
                'total_return': episode_data['total_return'],
                'trades_made': episode_data['trades_made'],
                'max_drawdown': episode_data['max_drawdown'],
                'policy_loss': policy_loss.item(),
                'value_loss': value_loss.item()
            }

            training_history.append(performance)

            # Update best performance
            if episode_data['total_reward'] > best_performance['reward']:
                best_performance.update({
                    'reward': episode_data['total_reward'],
                    'balance': episode_data['final_balance'],
                    'return': episode_data['total_return'],
                    'episode': episode
                })

                # Save best model
                model_path = "models/enhanced_300_model.pth"
                Path(model_path).parent.mkdir(parents=True, exist_ok=True)
                torch.save({
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'episode': episode,
                    'performance': best_performance,
                    'training_history': training_history
                }, model_path)

            # Logging
            if episode % 20 == 0:
                logger.info(f"Episode {episode}: Balance=${performance['final_balance']:.2f}, "
                           f"Return={performance['total_return']:+.2f}%, "
                           f"Trades={performance['trades_made']}")

        logger.info("Enhanced training completed!")
        logger.info(f"Best performance: Episode {best_performance['episode']}, "
                   f"Balance=${best_performance['balance']:.2f}, "
                   f"Return={best_performance['return']:+.2f}%")

        return {
            'training_history': training_history,
            'best_performance': best_performance
        }


async def main():
    """Main enhanced training function."""
    print("🚀 ENHANCED $300 CAPITAL TRAINING")
    print("=" * 50)
    print("💰 Starting Capital: $300")
    print("🎯 Risk per Trade: 5% = $15")
    print("📊 Target: Realistic returns with proper risk management")
    print("=" * 50)

    # Initialize enhanced training
    trainer = Enhanced300Training()

    # Run enhanced training
    results = await trainer.enhanced_training(episodes=100)

    # Display results
    print("\n📊 TRAINING RESULTS")
    print("-" * 30)

    best = results['best_performance']
    print(f"🏆 Best Episode: {best['episode']}")
    print(f"💰 Best Balance: ${best['balance']:.2f}")
    print(f"📈 Best Return: {best['return']:+.2f}%")
    print(f"🎯 Best Reward: {best['reward']:.4f}")

    # Show progression
    history = results['training_history']
    if len(history) >= 10:
        recent = history[-10:]
        avg_balance = np.mean([h['final_balance'] for h in recent])
        avg_return = np.mean([h['total_return'] for h in recent])

        print(f"\n📈 Recent Performance (last 10 episodes):")
        print(f"   Average Balance: ${avg_balance:.2f}")
        print(f"   Average Return: {avg_return:+.2f}%")

    print(f"\n✅ Enhanced model saved to: models/enhanced_300_model.pth")
    print("🚀 Ready for evaluation and live trading!")


if __name__ == "__main__":
    asyncio.run(main())
