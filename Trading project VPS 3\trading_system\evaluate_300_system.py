"""
Comprehensive Evaluation of $300 Trading System
Tests the enhanced model with realistic scenarios and expectations
"""
import asyncio
import sys
import numpy as np
import torch
from datetime import datetime, timedelta, timezone
from pathlib import Path
from typing import Dict, List
import logging

# Fix for Windows event loop issue
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# Add src to path
sys.path.append(str(Path(__file__).parent))

from src.data.binance_fetcher import BinanceDataFetcher
from src.trading.environment import GridTradingEnv, Action
from src.ml.integrated_training import TradingFeatureExtractor, SimpleTCNPPO

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class System300Evaluator:
    """Comprehensive evaluator for $300 trading system."""

    def __init__(self):
        self.initial_capital = 300.0
        self.feature_extractor = TradingFeatureExtractor(lookback_window=24)
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        # Load enhanced model
        self.model = None
        self.load_enhanced_model()

    def load_enhanced_model(self):
        """Load the enhanced model trained for $300 capital."""
        model_path = "models/enhanced_300_model.pth"

        if Path(model_path).exists():
            feature_dim = self.feature_extractor.get_feature_dim()
            self.model = SimpleTCNPPO(feature_dim, hidden_dim=64).to(self.device)

            checkpoint = torch.load(model_path, map_location=self.device)
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.model.eval()

            logger.info(f"Loaded enhanced model from {model_path}")

            # Show training performance
            if 'best_performance' in checkpoint:
                best = checkpoint['best_performance']
                logger.info(f"Model best performance: ${best['balance']:.2f} ({best['return']:+.2f}%)")
        else:
            logger.warning(f"Enhanced model not found at {model_path}")
            logger.info("Run enhanced_300_training.py first to train the model")

    async def get_fresh_test_data(self, days: int = 7) -> List:
        """Get fresh test data for evaluation."""
        logger.info(f"Fetching {days} days of fresh test data...")

        async with BinanceDataFetcher() as fetcher:
            end_time = datetime.now(timezone.utc)
            start_time = end_time - timedelta(days=days)

            response = await fetcher.fetch_historical_data(
                symbol="BTCUSDT",
                interval="1h",
                start_time=start_time,
                end_time=end_time
            )

            if not response.success:
                raise RuntimeError(f"Failed to fetch test data: {response.error}")

            logger.info(f"Fetched {len(response.data)} test candles")
            return response.data

    def evaluate_model_performance(self, candles: List) -> Dict:
        """Evaluate model performance on test data."""
        if self.model is None:
            return {'error': 'No model loaded'}

        env = GridTradingEnv(
            initial_balance=self.initial_capital,
            risk_per_trade=0.05,
            grid_spacing=0.0025,
            take_profit_multiplier=2.0,
            fee_rate=0.001
        )

        env.reset(candles[0].close, candles[0].timestamp)

        # Track detailed performance
        performance = {
            'trades': [],
            'balance_history': [self.initial_capital],
            'equity_history': [self.initial_capital],
            'actions_taken': [],
            'timestamps': [candles[0].timestamp]
        }

        trades_made = 0
        max_trades = 15  # Reasonable limit for test period

        for i in range(self.feature_extractor.lookback_window, len(candles)):
            if trades_made >= max_trades:
                break

            try:
                # Extract features
                features = self.feature_extractor.extract_features(candles[:i+1])
                state_tensor = torch.FloatTensor(features).unsqueeze(0).to(self.device)

                # Get action from model
                with torch.no_grad():
                    action_idx, _, _ = self.model.act(state_tensor, deterministic=True)

                # Convert to environment action
                action_map = {0: Action.BUY, 1: Action.SELL, 2: Action.HOLD}
                action = action_map[action_idx]

                # Execute step
                candle = candles[i]
                prev_balance = env.balance
                prev_positions = len(env.positions)

                _, _, _, _ = env.step(action, candle.close, candle.timestamp, candle)

                # Track performance
                performance['balance_history'].append(env.balance)
                performance['equity_history'].append(env.equity)
                performance['timestamps'].append(candle.timestamp)

                # Record action
                performance['actions_taken'].append({
                    'timestamp': candle.timestamp,
                    'action': action.name,
                    'price': candle.close,
                    'balance_before': prev_balance,
                    'balance_after': env.balance
                })

                # Check for new trades
                if len(env.positions) > prev_positions:
                    trades_made += 1

                # Record completed trades
                if len(env.closed_positions) > len(performance['trades']):
                    latest_trade = env.closed_positions[-1]
                    performance['trades'].append({
                        'entry_time': latest_trade.entry_time,
                        'exit_time': candle.timestamp,
                        'entry_price': latest_trade.entry_price,
                        'exit_price': latest_trade.current_price,
                        'pnl': latest_trade.pnl,
                        'pnl_pct': latest_trade.pnl_pct,
                        'type': latest_trade.position_type
                    })

            except ValueError:
                continue

        # Calculate final metrics
        final_balance = env.balance
        total_return = (final_balance / self.initial_capital - 1) * 100

        # Calculate max drawdown
        peak = self.initial_capital
        max_drawdown = 0
        for balance in performance['balance_history']:
            if balance > peak:
                peak = balance
            drawdown = (peak - balance) / peak
            if drawdown > max_drawdown:
                max_drawdown = drawdown

        # Win rate calculation
        winning_trades = [t for t in performance['trades'] if t['pnl'] > 0]
        win_rate = len(winning_trades) / len(performance['trades']) * 100 if performance['trades'] else 0

        performance.update({
            'final_balance': final_balance,
            'total_return': total_return,
            'max_drawdown': max_drawdown * 100,
            'total_trades': len(performance['trades']),
            'winning_trades': len(winning_trades),
            'win_rate': win_rate,
            'trades_made': trades_made
        })

        return performance

    def evaluate_baseline_strategies(self, candles: List) -> Dict:
        """Evaluate baseline strategies for comparison."""
        baselines = {}

        # Buy and Hold strategy
        buy_hold_return = (candles[-1].close / candles[0].close - 1) * 100
        baselines['buy_hold'] = {
            'final_balance': self.initial_capital * (1 + buy_hold_return / 100),
            'total_return': buy_hold_return,
            'max_drawdown': 0,  # Simplified
            'total_trades': 1,
            'win_rate': 100 if buy_hold_return > 0 else 0
        }

        # Random strategy (simulated)
        np.random.seed(42)  # For reproducibility
        random_returns = np.random.normal(0, 0.02, 10)  # 10 random trades with 2% std
        random_balance = self.initial_capital
        for ret in random_returns:
            random_balance *= (1 + ret)

        baselines['random'] = {
            'final_balance': random_balance,
            'total_return': (random_balance / self.initial_capital - 1) * 100,
            'max_drawdown': 15,  # Estimated
            'total_trades': 10,
            'win_rate': 50
        }

        return baselines

    async def comprehensive_evaluation(self) -> Dict:
        """Run comprehensive evaluation of the $300 system."""
        logger.info("🔬 Starting Comprehensive $300 System Evaluation")
        logger.info("=" * 60)

        if self.model is None:
            logger.error("No model available for evaluation")
            return {'error': 'No model loaded'}

        # Get test data
        test_data = await self.get_fresh_test_data(days=14)  # 2 weeks

        # Evaluate model
        logger.info("Evaluating enhanced ML model...")
        ml_performance = self.evaluate_model_performance(test_data)

        # Evaluate baselines
        logger.info("Evaluating baseline strategies...")
        baseline_performance = self.evaluate_baseline_strategies(test_data)

        # Combine results
        results = {
            'ml_model': ml_performance,
            'baselines': baseline_performance,
            'test_period': {
                'start': test_data[0].timestamp,
                'end': test_data[-1].timestamp,
                'candles': len(test_data),
                'price_start': test_data[0].close,
                'price_end': test_data[-1].close,
                'market_return': (test_data[-1].close / test_data[0].close - 1) * 100
            }
        }

        return results

    def generate_evaluation_report(self, results: Dict):
        """Generate comprehensive evaluation report."""
        print("\n📊 COMPREHENSIVE $300 SYSTEM EVALUATION REPORT")
        print("=" * 60)

        if 'error' in results:
            print(f"❌ Evaluation failed: {results['error']}")
            return

        # Test period info
        test_info = results['test_period']
        print(f"📅 Test Period: {test_info['start']} to {test_info['end']}")
        print(f"📈 Market Performance: {test_info['market_return']:+.2f}%")
        print(f"💵 Price Range: ${test_info['price_start']:,.2f} → ${test_info['price_end']:,.2f}")

        # Performance comparison
        print(f"\n🏆 PERFORMANCE COMPARISON")
        print("-" * 40)

        strategies = [
            ("Enhanced ML Model", results['ml_model']),
            ("Buy & Hold", results['baselines']['buy_hold']),
            ("Random Strategy", results['baselines']['random'])
        ]

        print(f"{'Strategy':<20} {'Balance':<12} {'Return':<10} {'Trades':<8} {'Win Rate':<10}")
        print("-" * 60)

        for name, perf in strategies:
            if 'error' not in perf:
                print(f"{name:<20} ${perf['final_balance']:<11.2f} {perf['total_return']:+7.2f}% "
                      f"{perf['total_trades']:<7} {perf['win_rate']:6.1f}%")

        # Detailed ML model analysis
        ml_perf = results['ml_model']
        if 'error' not in ml_perf:
            print(f"\n🤖 DETAILED ML MODEL ANALYSIS")
            print("-" * 35)
            print(f"💰 Final Balance: ${ml_perf['final_balance']:.2f}")
            print(f"📈 Total Return: {ml_perf['total_return']:+.2f}%")
            print(f"📉 Max Drawdown: {ml_perf['max_drawdown']:.2f}%")
            print(f"🎯 Total Trades: {ml_perf['total_trades']}")
            print(f"🏆 Win Rate: {ml_perf['win_rate']:.1f}%")

            # Risk assessment
            if ml_perf['total_return'] > 0:
                print(f"✅ Assessment: Profitable")
            elif ml_perf['total_return'] > -5:
                print(f"⚠️  Assessment: Minor loss (acceptable)")
            else:
                print(f"❌ Assessment: Needs improvement")

            # Trade analysis
            if ml_perf['trades']:
                avg_pnl = np.mean([t['pnl'] for t in ml_perf['trades']])
                print(f"💵 Average P&L per trade: ${avg_pnl:.2f}")

        print(f"\n✅ Evaluation completed successfully!")


async def main():
    """Main evaluation function."""
    print("🔬 $300 TRADING SYSTEM EVALUATION")
    print("Testing enhanced ML model with realistic expectations")
    print()

    # Initialize evaluator
    evaluator = System300Evaluator()

    # Run comprehensive evaluation
    results = await evaluator.comprehensive_evaluation()

    # Generate report
    evaluator.generate_evaluation_report(results)


if __name__ == "__main__":
    asyncio.run(main())
