"""
Simple Demo: 5 Trades with Real Market Data
Shows entry/exit, stop loss, take profit, 5% risk with compounding
"""
import asyncio
import sys
from datetime import datetime, timedelta, timezone

# Fix for Windows event loop issue
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

from src.data.binance_fetcher import BinanceDataFetcher
from src.trading.environment import GridTradingEnv, Action


async def simple_5_trades_demo():
    """Simple demo of 5 trades with real data."""
    print("🎯 5 SAMPLE TRADES DEMO")
    print("=" * 50)
    
    # Get real market data
    async with BinanceDataFetcher() as fetcher:
        response = await fetcher.fetch_klines("BTCUSDT", "1h", limit=20)
        candles = response.data
    
    print(f"✅ Using real BTC/USDT data: {len(candles)} candles")
    print(f"📅 Latest: {candles[-1].timestamp}")
    print(f"💰 Current Price: ${candles[-1].close:,.2f}")
    
    # Initialize trading environment
    env = GridTradingEnv(
        initial_balance=10000.0,
        risk_per_trade=0.05,  # 5% risk
        grid_spacing=0.0025,  # 0.25%
        take_profit_multiplier=2.0,  # 2:1 R:R
        fee_rate=0.001
    )
    
    env.reset(candles[0].close, candles[0].timestamp)
    
    print(f"\n💼 Starting Balance: ${env.balance:,.2f}")
    print(f"🎯 Risk per Trade: 5% = ${env.balance * 0.05:,.2f}")
    print(f"📊 Grid: 0.25% spacing, 0.5% TP, 0.125% SL")
    
    # Execute exactly 5 trades by forcing entries
    trades = [
        ("BUY", candles[2], "Dip opportunity"),
        ("SELL", candles[5], "Resistance level"),
        ("BUY", candles[8], "Support bounce"),
        ("SELL", candles[12], "Overbought"),
        ("BUY", candles[15], "Final dip")
    ]
    
    print(f"\n{'='*50}")
    print("🔥 EXECUTING 5 SAMPLE TRADES")
    print(f"{'='*50}")
    
    for trade_num, (direction, candle, reason) in enumerate(trades, 1):
        # Close any existing position first
        if env.positions:
            env.step(Action.HOLD, candle.close, candle.timestamp)
        
        # Enter new position
        action = Action.BUY if direction == "BUY" else Action.SELL
        prev_balance = env.balance
        
        env.step(action, candle.close, candle.timestamp)
        
        if env.positions:
            pos = env.positions[0]
            risk_amount = prev_balance * 0.05
            
            print(f"\n📈 TRADE #{trade_num}")
            print(f"⏰ Time: {candle.timestamp}")
            print(f"📊 Signal: {reason}")
            print(f"🎯 Direction: {'🟢 LONG' if direction == 'BUY' else '🔴 SHORT'}")
            print(f"💰 Entry: ${pos.entry_price:,.2f}")
            print(f"📏 Size: {pos.size:.6f} BTC (${pos.entry_price * pos.size:,.2f})")
            print(f"🎯 Take Profit: ${pos.take_profit:,.2f}")
            print(f"🛑 Stop Loss: ${pos.stop_loss:,.2f}")
            print(f"⚖️ Risk: ${risk_amount:,.2f} (5% of ${prev_balance:,.2f})")
            print(f"💼 Balance: ${env.balance:,.2f}")
            
            # Simulate exit after 2-3 candles
            exit_candle_idx = min(candles.index(candle) + 2, len(candles) - 1)
            exit_candle = candles[exit_candle_idx]
            
            # Force position closure by moving to exit price
            env.step(Action.HOLD, exit_candle.close, exit_candle.timestamp)
            
            if env.closed_positions:
                closed_pos = env.closed_positions[-1]
                
                # Determine exit type
                if direction == "BUY":
                    if exit_candle.close >= pos.take_profit:
                        exit_type = "🎯 TAKE PROFIT"
                    elif exit_candle.close <= pos.stop_loss:
                        exit_type = "🛑 STOP LOSS"
                    else:
                        exit_type = "⏰ TIME EXIT"
                else:  # SELL
                    if exit_candle.close <= pos.take_profit:
                        exit_type = "🎯 TAKE PROFIT"
                    elif exit_candle.close >= pos.stop_loss:
                        exit_type = "🛑 STOP LOSS"
                    else:
                        exit_type = "⏰ TIME EXIT"
                
                pnl_emoji = "💰" if closed_pos.pnl > 0 else "💸"
                
                print(f"🔚 EXIT: {exit_type}")
                print(f"💰 Exit Price: ${exit_candle.close:,.2f}")
                print(f"{pnl_emoji} P&L: ${closed_pos.pnl:,.2f} ({closed_pos.pnl_pct:+.2%})")
                print(f"💼 New Balance: ${env.balance:,.2f}")
                print(f"📈 Total Return: {((env.balance/10000-1)*100):+.2f}%")
    
    # Final summary
    print(f"\n{'='*50}")
    print("📊 FINAL SUMMARY")
    print(f"{'='*50}")
    
    metrics = env.get_metrics()
    
    print(f"💼 Starting: ${10000:,.2f}")
    print(f"💰 Final: ${metrics['balance']:,.2f}")
    print(f"📈 Return: {metrics['return_pct']:+.2f}%")
    print(f"📉 Max DD: {metrics['max_drawdown']:.2f}%")
    print(f"🎯 Trades: {int(metrics['trades'])}")
    print(f"🏆 Win Rate: {metrics['win_rate']:.1f}%")
    
    # Individual trade breakdown
    print(f"\n📋 TRADE BREAKDOWN:")
    total_pnl = 0
    for i, pos in enumerate(env.closed_positions, 1):
        total_pnl += pos.pnl
        pnl_emoji = "💰" if pos.pnl > 0 else "💸"
        print(f"  {i}. {pos.position_type.upper()}: ${pos.pnl:,.2f} ({pos.pnl_pnl:.2%}) {pnl_emoji}")
    
    print(f"\n💰 Total P&L: ${total_pnl:,.2f}")
    print(f"📊 Compounding: Each trade uses 5% of current balance")
    print(f"✅ Demo shows real risk management with live market data!")


if __name__ == "__main__":
    asyncio.run(simple_5_trades_demo())
