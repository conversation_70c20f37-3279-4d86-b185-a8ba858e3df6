"""
Data models for the trading system.
"""
from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field, validator, root_validator, PositiveFloat

class OHLCVSchema(BaseModel):
    """OHLCV data model with validation."""
    timestamp: datetime
    open: PositiveFloat
    high: PositiveFloat
    low: PositiveFloat
    close: PositiveFloat
    volume: float = Field(..., ge=0)
    symbol: str
    interval: str

    @root_validator
    def validate_ohlc_prices(cls, values):
        """Validate that OHLC prices are consistent."""
        if all(k in values for k in ['open', 'high', 'low', 'close']):
            open_price = values['open']
            high_price = values['high']
            low_price = values['low']
            close_price = values['close']

            # High must be the highest price
            if high_price < max(open_price, close_price, low_price):
                raise ValueError('High must be >= open, low, and close')

            # Low must be the lowest price
            if low_price > min(open_price, high_price, close_price):
                raise ValueError('Low must be <= open, high, and close')

        return values

class DataFetchRequest(BaseModel):
    """Request model for fetching OHLCV data."""
    symbol: str
    interval: str = '1h'
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    limit: int = 1000

class DataFetchResponse(BaseModel):
    """Response model for data fetch operations."""
    success: bool
    data: Optional[list[OHLCVSchema]] = None
    error: Optional[str] = None
    metadata: dict = Field(default_factory=dict)
