"""
Data handling and preprocessing for the trading system.
Handles data fetching, cleaning, and train/test splitting.
"""
import os
import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Tuple, Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataHandler:
    """Handles data loading, cleaning, and splitting for the trading system."""
    
    def __init__(self, api_key: str, api_secret: str, market: str, interval: str, 
                 data_dir: str, db_path: Optional[str] = None):
        """Initialize the DataHandler.
        
        Args:
            api_key: Exchange API key
            api_secret: Exchange API secret
            market: Market symbol (e.g., 'BTC/USDT')
            interval: Candle interval (e.g., '1h')
            data_dir: Directory to store data files
            db_path: Path to SQLite database (optional)
        """
        self.api_key = api_key
        self.api_secret = api_secret
        self.market = market
        self.interval = interval
        self.data_dir = data_dir
        self.db_path = db_path or os.path.join(data_dir, 'market_data.db')
        
        # Create data directory if it doesn't exist
        os.makedirs(data_dir, exist_ok=True)
        
        # Initialize database
        self._init_db()
    
    def _init_db(self):
        """Initialize the SQLite database with required tables."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS ohlcv (
                    timestamp INTEGER PRIMARY KEY,
                    open REAL,
                    high REAL,
                    low REAL,
                    close REAL,
                    volume REAL,
                    market TEXT,
                    interval TEXT
                )
            ''')
            conn.commit()
    
    def fetch_historical_data(self, start_date: str, end_date: str) -> pd.DataFrame:
        """Fetch historical OHLCV data for the specified date range.
        
        Args:
            start_date: Start date in 'YYYY-MM-DD' format
            end_date: End date in 'YYYY-MM-DD' format
            
        Returns:
            DataFrame with OHLCV data
        """
        logger.info(f"Fetching {self.market} data from {start_date} to {end_date}")
        
        # In a real implementation, this would fetch from an exchange API
        # For now, we'll return a mock DataFrame
        date_range = pd.date_range(start=start_date, end=end_date, freq='1H')
        n = len(date_range)
        
        # Generate mock data
        np.random.seed(42)
        base = np.cumsum(np.random.randn(n) * 0.005 + 0.001) + 100
        df = pd.DataFrame({
            'open': base * (1 + np.random.randn(n) * 0.01),
            'high': base * (1 + np.random.randn(n) * 0.01).clip(1.001, 1.02),
            'low': base * (1 - np.random.randn(n) * 0.01).clip(1.001, 1.02),
            'close': base,
            'volume': np.random.lognormal(5, 1, n)
        }, index=date_range)
        
        # Validate data before returning
        if not self.validate_data(df):
            raise ValueError("Data validation failed")
        
        # Save to database
        self._save_to_db(df, self.market, self.interval)
        
        return df
    
    def _save_to_db(self, df: pd.DataFrame, market: str, interval: str):
        """Save OHLCV data to the database."""
        with sqlite3.connect(self.db_path) as conn:
            # Delete existing data for this market/interval
            conn.execute(
                'DELETE FROM ohlcv WHERE market = ? AND interval = ?',
                (market, interval)
            )
            
            # Insert new data
            df = df.copy()
            df['market'] = market
            df['interval'] = interval
            df['timestamp'] = df.index.astype(np.int64) // 10**9  # Convert to Unix timestamp
            
            df[['timestamp', 'open', 'high', 'low', 'close', 'volume', 'market', 'interval']].to_sql(
                'ohlcv', conn, if_exists='append', index=False
            )
    
    def split_train_test_oot(self, df: pd.DataFrame, test_days: int = 30) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """Split data into training and out-of-sample test sets.
        
        Args:
            df: Input DataFrame with datetime index
            test_days: Number of days to use for testing
            
        Returns:
            Tuple of (train_df, test_df)
        """
        if not isinstance(df.index, pd.DatetimeIndex):
            raise ValueError("DataFrame must have a DatetimeIndex")
        
        # Sort by index to ensure chronological order
        df = df.sort_index()
        
        # Calculate split point
        split_date = df.index[-1] - pd.Timedelta(days=test_days)
        
        # Split data
        train_df = df[df.index <= split_date]
        test_df = df[df.index > split_date]
        
        # Log split information
        logger.info(f"Training data: {len(train_df)} samples from {train_df.index[0]} to {train_df.index[-1]}")
        logger.info(f"Test data: {len(test_df)} samples from {test_df.index[0]} to {test_df.index[-1]}")
        
        return train_df, test_df
    
    def get_intermarket(self, symbol: str, interval: str, 
                       start_date: str, end_date: str) -> pd.Series:
        """Get intermarket data (e.g., ETH/BTC ratio).
        
        Args:
            symbol: Market symbol (e.g., 'ETH/BTC')
            interval: Candle interval
            start_date: Start date in 'YYYY-MM-DD' format
            end_date: End date in 'YYYY-MM-DD' format
            
        Returns:
            Series with intermarket data
        """
        logger.info(f"Fetching {symbol} data from {start_date} to {end_date}")
        
        # In a real implementation, this would fetch the actual ratio
        # For now, return a mock series
        date_range = pd.date_range(start=start_date, end=end_date, freq='1H')
        n = len(date_range)
        
        # Generate mock ratio data
        np.random.seed(42)
        ratio = 0.05 * (1 + np.cumsum(np.random.randn(n) * 0.001))
        
        return pd.Series(ratio, index=date_range, name=f'{symbol}_ratio')
    
    def validate_data(self, df: pd.DataFrame) -> bool:
        """Validate the OHLCV data.
        
        Args:
            df: DataFrame with OHLCV data
            
        Returns:
            bool: True if data is valid, False otherwise
        """
        required_columns = {'open', 'high', 'low', 'close', 'volume'}
        if not required_columns.issubset(df.columns):
            missing = required_columns - set(df.columns)
            logger.error(f"Missing required columns: {missing}")
            return False
        
        # Check for missing values
        if df.isnull().any().any():
            logger.warning("Data contains missing values")
            return False
            
        # Check for OHLC consistency
        ohlc_checks = (
            (df['high'] >= df[['open', 'close']].max(axis=1)).all() and
            (df['low'] <= df[['open', 'close']].min(axis=1)).all()
        )
        if not ohlc_checks:
            logger.warning("OHLC consistency check failed")
            return False
            
        # Check for zero or negative prices
        price_columns = ['open', 'high', 'low', 'close']
        if (df[price_columns] <= 0).any().any():
            logger.warning("Price data contains zero or negative values")
            return False
            
        return True
    
    def check_data_continuity(self, df: pd.DataFrame, freq: str = '1H') -> bool:
        """Check for gaps in the time series data.
        
        Args:
            df: Input DataFrame with datetime index
            freq: Expected frequency of the data
            
        Returns:
            bool: True if data is continuous, False otherwise
        """
        if not isinstance(df.index, pd.DatetimeIndex):
            raise ValueError("DataFrame must have a DatetimeIndex")
            
        # Calculate time differences between consecutive timestamps
        time_diff = df.index.to_series().diff().dropna()
        expected_interval = pd.Timedelta(freq)
        
        # Find gaps larger than expected
        gaps = time_diff[time_diff > expected_interval * 1.5]  # Allow 50% tolerance
        
        if not gaps.empty:
            logger.warning(f"Found {len(gaps)} gaps in the data:")
            for idx, gap in gaps.items():
                logger.warning(f"- Gap of {gap} at {idx}")
            return False
            
        return True
