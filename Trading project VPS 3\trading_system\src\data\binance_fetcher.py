"""
Binance Exchange Data Fetcher

This module provides functionality to fetch OHLCV data from Binance with retry
mechanism and data validation.
"""
import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Union

import aiohttp
import pandas as pd
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
    before_sleep_log
)

from .models import OHLCVSchema, DataFetchRequest, DataFetchResponse

logger = logging.getLogger(__name__)

class BinanceDataFetcher:
    """Handles data fetching from Binance API with retry mechanism."""

    BASE_URL = "https://api.binance.com/api/v3"

    def __init__(self, api_key: str = None, session: aiohttp.ClientSession = None):
        """Initialize the Binance data fetcher.

        Args:
            api_key: Optional Binance API key
            session: Optional aiohttp ClientSession for connection pooling
        """
        self.api_key = api_key
        self._session = session
        self._session_owner = session is None

    async def __aenter__(self):
        if self._session is None:
            # Configure connector for Windows compatibility
            connector = aiohttp.TCPConnector(
                use_dns_cache=False,
                ttl_dns_cache=300,
                limit=100,
                limit_per_host=30
            )
            self._session = aiohttp.ClientSession(connector=connector)
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self._session_owner and self._session:
            await self._session.close()

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((aiohttp.ClientError, asyncio.TimeoutError)),
        before_sleep=before_sleep_log(logger, logging.WARNING)
    )
    async def _make_request(self, endpoint: str, params: Optional[Dict] = None) -> Dict:
        """Make an API request with retry logic."""
        url = f"{self.BASE_URL}/{endpoint}"

        try:
            async with self._session.get(url, params=params) as response:
                response.raise_for_status()
                return await response.json()
        except aiohttp.ClientError as e:
            logger.error(f"Request failed: {e}")
            raise

    async def fetch_klines(
        self,
        symbol: str,
        interval: str = '1h',
        start_time: Optional[Union[int, datetime]] = None,
        end_time: Optional[Union[int, datetime]] = None,
        limit: int = 1000
    ) -> DataFetchResponse:
        """Fetch kline/candlestick data from Binance.

        Args:
            symbol: Trading pair symbol (e.g., 'BTCUSDT')
            interval: Kline interval (default: '1h')
            start_time: Start time as timestamp (ms) or datetime
            end_time: End time as timestamp (ms) or datetime
            limit: Maximum number of candles to fetch (max 1000)

        Returns:
            DataFetchResponse containing the OHLCV data or error
        """
        params = {
            'symbol': symbol.upper(),
            'interval': interval,
            'limit': min(limit, 1000)  # Binance max limit
        }

        # Convert datetime to timestamp if needed
        if isinstance(start_time, datetime):
            params['startTime'] = int(start_time.timestamp() * 1000)
        elif start_time is not None:
            params['startTime'] = int(start_time)

        if isinstance(end_time, datetime):
            params['endTime'] = int(end_time.timestamp() * 1000)
        elif end_time is not None:
            params['endTime'] = int(end_time)

        try:
            data = await self._make_request('klines', params)
            candles = []

            for candle in data:
                try:
                    candles.append(OHLCVSchema(
                        timestamp=datetime.fromtimestamp(candle[0] / 1000, tz=timezone.utc),
                        open=float(candle[1]),
                        high=float(candle[2]),
                        low=float(candle[3]),
                        close=float(candle[4]),
                        volume=float(candle[5]),
                        symbol=symbol.upper(),
                        interval=interval
                    ))
                except Exception as e:
                    logger.warning(f"Failed to parse candle: {e}")

            return DataFetchResponse(
                success=True,
                data=candles,
                metadata={
                    'symbol': symbol,
                    'interval': interval,
                    'count': len(candles),
                    'start_time': candles[0].timestamp if candles else None,
                    'end_time': candles[-1].timestamp if candles else None
                }
            )

        except Exception as e:
            return DataFetchResponse(
                success=False,
                error=str(e),
                metadata={
                    'symbol': symbol,
                    'interval': interval,
                    'params': params
                }
            )

    async def fetch_historical_data(
        self,
        symbol: str,
        interval: str = '1h',
        start_time: Optional[Union[int, datetime]] = None,
        end_time: Optional[Union[int, datetime]] = None,
        max_candles: Optional[int] = None
    ) -> DataFetchResponse:
        """Fetch historical OHLCV data with pagination.

        Args:
            symbol: Trading pair symbol (e.g., 'BTCUSDT')
            interval: Kline interval (default: '1h')
            start_time: Start time as timestamp (ms) or datetime
            end_time: End time as timestamp (ms) or datetime (default: now)
            max_candles: Maximum number of candles to fetch in total

        Returns:
            DataFetchResponse containing the OHLCV data or error
        """
        if end_time is None:
            end_time = datetime.now(timezone.utc)
        elif isinstance(end_time, datetime):
            end_time = end_time.replace(tzinfo=timezone.utc)

        all_candles = []
        current_end = end_time

        try:
            while True:
                # Fetch a batch of candles
                response = await self.fetch_klines(
                    symbol=symbol,
                    interval=interval,
                    end_time=current_end,
                    limit=1000  # Max allowed by Binance
                )

                if not response.success or not response.data:
                    break

                # Filter out candles before start_time
                if start_time is not None:
                    response.data = [
                        c for c in response.data
                        if c.timestamp >= start_time
                    ]

                all_candles.extend(response.data)

                # Check if we've reached max_candles or start_time
                if (max_candles and len(all_candles) >= max_candles) or \
                   (start_time is not None and response.data and
                    response.data[0].timestamp < start_time):
                    break

                # Update end_time for next batch
                if len(response.data) < 1000:
                    break

                current_end = response.data[0].timestamp

                # Small delay to avoid rate limiting
                await asyncio.sleep(0.1)

            # Sort by timestamp and apply max_candles limit
            all_candles.sort(key=lambda x: x.timestamp)
            if max_candles:
                all_candles = all_candles[-max_candles:]

            return DataFetchResponse(
                success=True,
                data=all_candles,
                metadata={
                    'symbol': symbol,
                    'interval': interval,
                    'count': len(all_candles),
                    'start_time': all_candles[0].timestamp if all_candles else None,
                    'end_time': all_candles[-1].timestamp if all_candles else None
                }
            )

        except Exception as e:
            return DataFetchResponse(
                success=False,
                error=str(e),
                metadata={
                    'symbol': symbol,
                    'interval': interval,
                    'start_time': start_time,
                    'end_time': end_time
                }
            )
