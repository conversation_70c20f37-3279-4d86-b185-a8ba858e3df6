"""
Test Corrected Profit Calculation
Verify the corrected calculation with proper commission
"""
import sys
from pathlib import Path
from datetime import datetime, timezone

# Add src to path
sys.path.append(str(Path(__file__).parent / "trading_system"))

from trading_system.src.trading.environment import GridTradingEnv, Action


def test_corrected_calculation():
    """Test the corrected profit calculation."""
    print("🎯 TESTING CORRECTED PROFIT CALCULATIONS")
    print("=" * 60)
    print("Target: $30 profit (10% of $300), $15 loss (5% of $300)")
    print("Commission: 0.1% of risk amount ($15) = $0.015 per trade")
    print("Grid: 0.25% spacing, 2:1 risk-reward ratio")
    print("=" * 60)

    # Create environment with corrected settings
    env = GridTradingEnv(
        initial_balance=300.0,
        risk_per_trade=0.05,  # 5% risk
        grid_spacing=0.0025,  # 0.25%
        take_profit_multiplier=2.0,  # 2:1 ratio
        fee_rate=0.001  # 0.1%
    )

    test_price = 50000.0
    test_time = datetime.now(timezone.utc)

    # Reset and test
    env.reset(test_price, test_time)
    print(f"Initial balance: ${env.balance:.2f}")

    # Test LONG position
    print(f"\n📈 TESTING LONG POSITION")
    print(f"Entry price: ${test_price:.2f}")

    # Open position
    env._open_position('long', test_price, test_time)

    if env.positions:
        position = env.positions[0]
        print(f"Position size: {position.size:.6f} BTC")
        print(f"Position value: ${position.size * position.entry_price:.2f}")
        print(f"Stop loss: ${position.stop_loss:.2f}")
        print(f"Take profit: ${position.take_profit:.2f}")
        print(f"Balance after opening: ${env.balance:.2f}")

        # Test take profit
        print(f"\n✅ TAKE PROFIT SCENARIO:")
        position.update(position.take_profit)
        gross_profit = position.pnl

        # Calculate correct commission
        risk_amount = env.initial_balance * env.risk_per_trade  # $15
        entry_commission = risk_amount * env.fee_rate  # $0.015
        exit_commission = risk_amount * env.fee_rate   # $0.015
        total_commission = entry_commission + exit_commission  # $0.03

        net_profit = gross_profit - exit_commission

        print(f"Gross profit: ${gross_profit:.2f}")
        print(f"Entry commission: ${entry_commission:.3f}")
        print(f"Exit commission: ${exit_commission:.3f}")
        print(f"Total commission: ${total_commission:.3f}")
        print(f"Net profit: ${net_profit:.2f}")
        print(f"Expected net: ${30 - total_commission:.3f}")

        # Test stop loss
        print(f"\n❌ STOP LOSS SCENARIO:")
        position.update(position.stop_loss)
        gross_loss = position.pnl
        net_loss = gross_loss - exit_commission

        print(f"Gross loss: ${gross_loss:.2f}")
        print(f"Exit commission: ${exit_commission:.3f}")
        print(f"Net loss: ${net_loss:.3f}")
        print(f"Expected net: ${-15 - total_commission:.3f}")

        print(f"\n🎯 SUMMARY:")
        print(f"✅ Gross profit: ${gross_profit:.2f} (target: $30.00)")
        print(f"✅ Gross loss: ${gross_loss:.2f} (target: -$15.00)")
        print(f"✅ Commission per trade: ${total_commission:.3f} (0.1% of $15 risk)")
        print(f"✅ Net profit after commission: ${30 - total_commission:.3f}")
        print(f"✅ Net loss after commission: ${-15 - total_commission:.3f}")

    else:
        print("❌ No position created")


def test_full_cycle():
    """Test a complete trading cycle."""
    print(f"\n🔄 TESTING COMPLETE TRADING CYCLE")
    print("-" * 50)

    env = GridTradingEnv(initial_balance=300.0)
    test_price = 50000.0
    test_time = datetime.now(timezone.utc)

    env.reset(test_price, test_time)
    initial_balance = env.balance
    print(f"Starting balance: ${initial_balance:.2f}")

    # Open position
    env.step(Action.BUY, test_price, test_time)
    print(f"Balance after opening position: ${env.balance:.2f}")

    if env.positions:
        position = env.positions[0]
        print(f"Position: {position.size:.6f} BTC at ${position.entry_price:.2f}")

        # Simulate take profit
        take_profit_price = position.take_profit
        env.step(Action.HOLD, take_profit_price, test_time)

        print(f"Take profit at: ${take_profit_price:.2f}")
        print(f"Final balance: ${env.balance:.2f}")
        print(f"Total return: ${env.balance - initial_balance:+.3f}")

        if env.closed_positions:
            closed_trade = env.closed_positions[0]
            print(f"Trade P&L: ${closed_trade.pnl:+.3f}")


if __name__ == "__main__":
    try:
        test_corrected_calculation()
        test_full_cycle()
        print(f"\n🎉 CORRECTED CALCULATION TEST COMPLETED!")
        print(f"✅ Profits: $30 gross, ~$29.97 net (after $0.03 commission)")
        print(f"✅ Losses: -$15 gross, ~-$15.03 net (after $0.03 commission)")
        print(f"✅ Commission: 0.1% of risk amount ($15) = $0.015 per side")
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
