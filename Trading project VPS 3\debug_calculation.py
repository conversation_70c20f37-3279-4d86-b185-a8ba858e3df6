"""
Debug the profit calculation step by step
"""

def debug_calculation():
    print("🔍 DEBUGGING PROFIT CALCULATION")
    print("=" * 50)
    
    # Parameters
    initial_balance = 300.0
    risk_per_trade = 0.05  # 5%
    grid_spacing = 0.0025  # 0.25%
    take_profit_multiplier = 2.0
    entry_price = 50000.0
    
    # Targets
    target_loss = initial_balance * risk_per_trade  # $15
    target_profit = target_loss * take_profit_multiplier  # $30
    
    print(f"Initial balance: ${initial_balance}")
    print(f"Target loss: ${target_loss}")
    print(f"Target profit: ${target_profit}")
    print(f"Entry price: ${entry_price}")
    
    # Current calculation (WRONG)
    stop_loss_pct = grid_spacing / 2  # 0.125%
    take_profit_pct = stop_loss_pct * take_profit_multiplier  # 0.25%
    
    print(f"\nCurrent approach:")
    print(f"Stop loss %: {stop_loss_pct*100:.3f}%")
    print(f"Take profit %: {take_profit_pct*100:.3f}%")
    
    # Wrong calculation
    position_size_wrong = target_loss / (entry_price * stop_loss_pct)
    position_value_wrong = position_size_wrong * entry_price
    
    print(f"Position size (wrong): {position_size_wrong:.6f}")
    print(f"Position value (wrong): ${position_value_wrong:.2f}")
    
    # Test the wrong calculation
    actual_loss_wrong = position_size_wrong * entry_price * stop_loss_pct
    actual_profit_wrong = position_size_wrong * entry_price * take_profit_pct
    
    print(f"Actual loss (wrong): ${actual_loss_wrong:.2f}")
    print(f"Actual profit (wrong): ${actual_profit_wrong:.2f}")
    
    print(f"\n" + "="*50)
    print("CORRECT APPROACH:")
    print("="*50)
    
    # CORRECT: We need to work backwards from the dollar targets
    # If we want to lose $15 on a 0.125% move, what position size do we need?
    
    # For a LONG position:
    # Loss occurs when price drops from entry_price to stop_loss
    # stop_loss = entry_price * (1 - stop_loss_pct)
    # Loss in dollars = position_size * (entry_price - stop_loss)
    # Loss in dollars = position_size * entry_price * stop_loss_pct
    
    # We want: position_size * entry_price * stop_loss_pct = target_loss
    # Therefore: position_size = target_loss / (entry_price * stop_loss_pct)
    
    # But wait - this is what we already have! Let me check the math...
    
    print(f"Math check:")
    print(f"target_loss = ${target_loss}")
    print(f"entry_price = ${entry_price}")
    print(f"stop_loss_pct = {stop_loss_pct}")
    print(f"entry_price * stop_loss_pct = ${entry_price * stop_loss_pct}")
    print(f"target_loss / (entry_price * stop_loss_pct) = {target_loss / (entry_price * stop_loss_pct)}")
    
    # AHA! The issue is that 0.125% of $50,000 is only $62.50
    # But we're trying to risk $15 on that move
    # This means we need a position size of $15 / $62.50 = 0.24 BTC
    # But 0.24 BTC at $50,000 = $12,000 position value!
    
    print(f"\nThe issue:")
    print(f"0.125% of ${entry_price} = ${entry_price * stop_loss_pct}")
    print(f"To lose $15 on a ${entry_price * stop_loss_pct} move:")
    print(f"Position size needed = {target_loss / (entry_price * stop_loss_pct):.6f} BTC")
    print(f"Position value needed = ${(target_loss / (entry_price * stop_loss_pct)) * entry_price:.2f}")
    
    print(f"\nBut we only have ${initial_balance} to trade with!")
    print(f"This means we can't achieve $15 loss with 0.125% stop loss")
    
    print(f"\n" + "="*50)
    print("SOLUTION: Adjust the grid spacing or use leverage")
    print("="*50)
    
    # Option 1: Calculate what % move we need for $15 loss with $300 position
    max_position_value = initial_balance * 0.9  # Use 90% of balance
    position_size_available = max_position_value / entry_price
    required_move_pct = target_loss / max_position_value
    
    print(f"Option 1 - Adjust stop loss %:")
    print(f"Max position value: ${max_position_value}")
    print(f"Position size available: {position_size_available:.6f} BTC")
    print(f"Required move % for $15 loss: {required_move_pct*100:.3f}%")
    
    # Option 2: Use leverage (simulate with larger position)
    leverage = 10  # 10x leverage
    leveraged_position_value = initial_balance * leverage
    leveraged_position_size = leveraged_position_value / entry_price
    
    print(f"\nOption 2 - Use 10x leverage:")
    print(f"Leveraged position value: ${leveraged_position_value}")
    print(f"Leveraged position size: {leveraged_position_size:.6f} BTC")
    
    # Test with leverage
    actual_loss_leveraged = leveraged_position_size * entry_price * stop_loss_pct
    actual_profit_leveraged = leveraged_position_size * entry_price * take_profit_pct
    
    print(f"Loss with leverage: ${actual_loss_leveraged:.2f}")
    print(f"Profit with leverage: ${actual_profit_leveraged:.2f}")


if __name__ == "__main__":
    debug_calculation()
