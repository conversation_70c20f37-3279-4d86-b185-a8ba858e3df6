
        <!DOCTYPE html>
        <html>
        <head>
            <title>Enhanced 1000-Cycle Evaluation Report</title>
            <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
            <style>
                
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: #f5f7fa; }
        .container { max-width: 1600px; margin: 0 auto; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 40px; border-radius: 15px; text-align: center; margin-bottom: 30px; }
        .header h1 { margin: 0; font-size: 2.5em; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .header p { margin: 10px 0 0 0; opacity: 0.9; font-size: 1.2em; }
        .header-stats { display: flex; justify-content: center; gap: 30px; margin-top: 20px; }
        .stat-item { text-align: center; }
        .stat-value { display: block; font-size: 2em; font-weight: bold; }
        .stat-label { display: block; opacity: 0.8; }
        .section { background: white; margin: 20px 0; padding: 25px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
        .section h2 { color: #2c3e50; margin-top: 0; border-bottom: 3px solid #3498db; padding-bottom: 10px; }
        .summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .summary-card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #3498db; }
        .summary-card h3 { margin-top: 0; color: #2c3e50; }
        .summary-card ul { margin: 0; padding-left: 20px; }
        .summary-card li { margin: 8px 0; }
        .chart-container { margin: 25px 0; height: 400px; }
        .chart-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .trades-table { width: 100%; border-collapse: collapse; margin: 20px 0; font-size: 0.9em; }
        .trades-table th { background: #3498db; color: white; padding: 12px 8px; text-align: center; }
        .trades-table td { padding: 8px; border-bottom: 1px solid #eee; text-align: center; }
        .trades-table .profit { background: #d4edda; }
        .trades-table .loss { background: #f8d7da; }
        .table-container { overflow-x: auto; }
        .trade-summary { background: #e8f4fd; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
        .trade-summary p { margin: 5px 0; }
        .equity-stats { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 20px; }
        .stat-card { background: #f8f9fa; padding: 20px; border-radius: 8px; }
        .stat-card h4 { margin-top: 0; color: #2c3e50; }
        .stat-card p { margin: 8px 0; }
        .commission-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .commission-card { background: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid #ffc107; }
        .commission-card h3 { margin-top: 0; color: #856404; }
        .commission-card p { margin: 8px 0; }
        .note { font-style: italic; color: #666; margin-top: 10px; }
        .ready-badge { background: #28a745; color: white; padding: 10px 20px; border-radius: 25px; font-weight: bold; display: inline-block; margin: 15px 0; }
        .caution-badge { background: #ffc107; color: #212529; padding: 10px 20px; border-radius: 25px; font-weight: bold; display: inline-block; margin: 15px 0; }
        .not-ready-badge { background: #dc3545; color: white; padding: 10px 20px; border-radius: 25px; font-weight: bold; display: inline-block; margin: 15px 0; }
        .readiness-overview { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px; }
        .readiness-card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745; }
        .readiness-score { text-align: center; margin: 15px 0; }
        .score-value { display: block; font-size: 3em; font-weight: bold; color: #28a745; }
        .score-label { display: block; color: #666; margin-top: 5px; }
        .criteria-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; }
        .criteria-item { background: #f8f9fa; padding: 15px; border-radius: 8px; }
        .criteria-item h4 { margin-top: 0; color: #2c3e50; }
        .criteria-status { font-weight: bold; padding: 8px; border-radius: 5px; text-align: center; }
        .criteria-status.met { background: #d4edda; color: #155724; }
        .criteria-status.not-met { background: #f8d7da; color: #721c24; }
        
            </style>
        </head>
        <body>
            <div class="container">
                
        <div class="header">
            <h1>🎯 Enhanced 1-Cycle Comprehensive Evaluation Report</h1>
            <p>60-Day Training + 30-Day Out-of-Sample Testing | Simple 5% Risk Model</p>
            <p>Optimized for Composite Metrics | Generated: 2025-05-26 17:50:17</p>
            <div class="ready-badge">🚀 READY FOR LIVE TRADING</div>
            <div class="header-stats">
                <div class="stat-item">
                    <span class="stat-value">1</span>
                    <span class="stat-label">Valid Cycles</span>
                </div>
                <div class="stat-item">
                    <span class="stat-value">0.796</span>
                    <span class="stat-label">Avg Composite</span>
                </div>
                <div class="stat-item">
                    <span class="stat-value">+4925.9%</span>
                    <span class="stat-label">Avg Return</span>
                </div>
                <div class="stat-item">
                    <span class="stat-value">1</span>
                    <span class="stat-label">Profitable</span>
                </div>
                <div class="stat-item"><span class="stat-value">100/100</span><span class="stat-label">Stability Score</span></div>
            </div>
        </div>
        
                
        <div class="section">
            <h2>📊 Executive Summary</h2>
            <div class="summary-grid">
                <div class="summary-card">
                    <h3>Performance Overview</h3>
                    <ul>
                        <li><strong>Success Rate:</strong> 100.0%</li>
                        <li><strong>Profitable Cycles:</strong> 1/1 (100.0%)</li>
                        <li><strong>Best Composite Score:</strong> 0.796</li>
                        <li><strong>Worst Composite Score:</strong> 0.796</li>
                    </ul>
                </div>
                <div class="summary-card">
                    <h3>Return Statistics</h3>
                    <ul>
                        <li><strong>Mean Return:</strong> +4925.86%</li>
                        <li><strong>Median Return:</strong> +4925.86%</li>
                        <li><strong>Best Return:</strong> +4925.86%</li>
                        <li><strong>Worst Return:</strong> +4925.86%</li>
                    </ul>
                </div>
                <div class="summary-card">
                    <h3>Trading Activity</h3>
                    <ul>
                        <li><strong>Avg Trades/Cycle:</strong> 326.0</li>
                        <li><strong>Min Trades:</strong> 326</li>
                        <li><strong>Max Trades:</strong> 326</li>
                        <li><strong>Std Deviation:</strong> 0.0</li>
                    </ul>
                </div>
            </div>
        </div>
        
                
        <div class="section">
            <h2>📈 Performance Distribution</h2>
            <div class="chart-grid">
                <div class="chart-container" id="composite-distribution"></div>
                <div class="chart-container" id="returns-distribution"></div>
            </div>
            <div class="chart-container" id="performance-categories"></div>
        </div>
        
                
        <div class="section">
            <h2>📋 Composite Metrics Breakdown</h2>
            <div class="composite-breakdown">
                
            <div class="metric-breakdown">
                <h4>Win Rate</h4>
                <p><strong>Mean:</strong> 0.4325</p>
                <p><strong>Std:</strong> 0.0000</p>
                <p><strong>Range:</strong> 0.4325 - 0.4325</p>
                <p><strong>Median:</strong> 0.4325</p>
            </div>
            
            <div class="metric-breakdown">
                <h4>Equity Growth</h4>
                <p><strong>Mean:</strong> 2104.6944</p>
                <p><strong>Std:</strong> 0.0000</p>
                <p><strong>Range:</strong> 2104.6944 - 2104.6944</p>
                <p><strong>Median:</strong> 2104.6944</p>
            </div>
            
            <div class="metric-breakdown">
                <h4>Sortino Ratio</h4>
                <p><strong>Mean:</strong> 14.1175</p>
                <p><strong>Std:</strong> 0.0000</p>
                <p><strong>Range:</strong> 14.1175 - 14.1175</p>
                <p><strong>Median:</strong> 14.1175</p>
            </div>
            
            <div class="metric-breakdown">
                <h4>Calmar Ratio</h4>
                <p><strong>Mean:</strong> 2083.2077</p>
                <p><strong>Std:</strong> 0.0000</p>
                <p><strong>Range:</strong> 2083.2077 - 2083.2077</p>
                <p><strong>Median:</strong> 2083.2077</p>
            </div>
            
            <div class="metric-breakdown">
                <h4>Profit Factor</h4>
                <p><strong>Mean:</strong> 1.3948</p>
                <p><strong>Std:</strong> 0.0000</p>
                <p><strong>Range:</strong> 1.3948 - 1.3948</p>
                <p><strong>Median:</strong> 1.3948</p>
            </div>
            
            <div class="metric-breakdown">
                <h4>Max Drawdown</h4>
                <p><strong>Mean:</strong> 0.9891</p>
                <p><strong>Std:</strong> 0.0000</p>
                <p><strong>Range:</strong> 0.9891 - 0.9891</p>
                <p><strong>Median:</strong> 0.9891</p>
            </div>
            
            <div class="metric-breakdown">
                <h4>Risk Of Ruin</h4>
                <p><strong>Mean:</strong> 0.8776</p>
                <p><strong>Std:</strong> 0.0000</p>
                <p><strong>Range:</strong> 0.8776 - 0.8776</p>
                <p><strong>Median:</strong> 0.8776</p>
            </div>
            
            <div class="metric-breakdown">
                <h4>Trade Frequency</h4>
                <p><strong>Mean:</strong> 10.8969</p>
                <p><strong>Std:</strong> 0.0000</p>
                <p><strong>Range:</strong> 10.8969 - 10.8969</p>
                <p><strong>Median:</strong> 10.8969</p>
            </div>
            
            </div>
        </div>
        
                
        <div class="section">
            <h2>🏆 Best Performing Cycles</h2>
            <div class="table-container">
                <table class="trades-table">
                    <thead>
                        <tr>
                            <th>Rank</th>
                            <th>Cycle</th>
                            <th>Composite Score</th>
                            <th>Return</th>
                            <th>Final Balance</th>
                            <th>Total Trades</th>
                            <th>Winning</th>
                            <th>Losing</th>
                            <th>Max Drawdown</th>
                            <th>Commission</th>
                        </tr>
                    </thead>
                    <tbody>
                        
            <tr>
                <td>1</td>
                <td>0</td>
                <td>0.7960</td>
                <td>+4925.86%</td>
                <td>$15077.59</td>
                <td>326</td>
                <td>141</td>
                <td>185</td>
                <td>98.91%</td>
                <td>$126.97</td>
            </tr>
            
                    </tbody>
                </table>
            </div>
        </div>
        
                
        <div class="section">
            <h2>📋 Trade-by-Trade Analysis (Best Cycle #0)</h2>
            <div class="trade-summary">
                <p><strong>Total Trades:</strong> 326</p>
                <p><strong>Total Commission Paid:</strong> $126.97</p>
                <p><strong>Final Balance:</strong> $15077.59</p>
                <p><strong>Total Return:</strong> +4925.86%</p>
            </div>
            <div class="table-container">
                <table class="trades-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Direction</th>
                            <th>Entry Time</th>
                            <th>Exit Time</th>
                            <th>Entry Price</th>
                            <th>Exit Price</th>
                            <th>Size</th>
                            <th>Gross P&L</th>
                            <th>Commission</th>
                            <th>Net P&L</th>
                            <th>Return %</th>
                            <th>Duration</th>
                            <th>Exit Reason</th>
                        </tr>
                    </thead>
                    <tbody>
                        
            <tr class="profit">
                <td>1</td>
                <td>SHORT</td>
                <td>2025-03-28 17:00</td>
                <td>2025-03-28 19:00</td>
                <td>$84008.45</td>
                <td>$83790.73</td>
                <td>0.142843</td>
                <td>$+30.00</td>
                <td>$0.03</td>
                <td>$+29.97</td>
                <td>+199.80%</td>
                <td>2.0h</td>
                <td>TAKE_PROFIT</td>
            </tr>
            
            <tr class="profit">
                <td>2</td>
                <td>LONG</td>
                <td>2025-03-28 19:00</td>
                <td>2025-03-28 21:00</td>
                <td>$83790.73</td>
                <td>$84052.29</td>
                <td>0.157521</td>
                <td>$+33.00</td>
                <td>$0.03</td>
                <td>$+32.96</td>
                <td>+199.80%</td>
                <td>2.0h</td>
                <td>TAKE_PROFIT</td>
            </tr>
            
            <tr class="loss">
                <td>3</td>
                <td>SHORT</td>
                <td>2025-03-28 21:00</td>
                <td>2025-03-28 22:00</td>
                <td>$84052.29</td>
                <td>$84480.35</td>
                <td>0.172718</td>
                <td>$-18.15</td>
                <td>$0.04</td>
                <td>$-18.18</td>
                <td>-100.20%</td>
                <td>1.0h</td>
                <td>STOP_LOSS</td>
            </tr>
            
            <tr class="loss">
                <td>4</td>
                <td>LONG</td>
                <td>2025-03-28 22:00</td>
                <td>2025-03-29 01:00</td>
                <td>$84480.35</td>
                <td>$84281.90</td>
                <td>0.163234</td>
                <td>$-17.24</td>
                <td>$0.03</td>
                <td>$-17.27</td>
                <td>-100.20%</td>
                <td>3.0h</td>
                <td>STOP_LOSS</td>
            </tr>
            
            <tr class="loss">
                <td>5</td>
                <td>SHORT</td>
                <td>2025-03-29 02:00</td>
                <td>2025-03-29 03:00</td>
                <td>$83851.40</td>
                <td>$84166.30</td>
                <td>0.156219</td>
                <td>$-16.37</td>
                <td>$0.03</td>
                <td>$-16.41</td>
                <td>-100.20%</td>
                <td>1.0h</td>
                <td>STOP_LOSS</td>
            </tr>
            
            <tr class="loss">
                <td>6</td>
                <td>LONG</td>
                <td>2025-03-29 03:00</td>
                <td>2025-03-29 05:00</td>
                <td>$84166.30</td>
                <td>$84012.64</td>
                <td>0.147837</td>
                <td>$-15.55</td>
                <td>$0.03</td>
                <td>$-15.58</td>
                <td>-100.20%</td>
                <td>2.0h</td>
                <td>STOP_LOSS</td>
            </tr>
            
            <tr class="profit">
                <td>7</td>
                <td>SHORT</td>
                <td>2025-03-29 05:00</td>
                <td>2025-03-29 06:00</td>
                <td>$84012.64</td>
                <td>$83787.28</td>
                <td>0.140687</td>
                <td>$+29.55</td>
                <td>$0.03</td>
                <td>$+29.52</td>
                <td>+199.80%</td>
                <td>1.0h</td>
                <td>TAKE_PROFIT</td>
            </tr>
            
            <tr class="loss">
                <td>8</td>
                <td>SHORT</td>
                <td>2025-03-29 06:00</td>
                <td>2025-03-29 08:00</td>
                <td>$83787.28</td>
                <td>$83934.95</td>
                <td>0.155158</td>
                <td>$-16.25</td>
                <td>$0.03</td>
                <td>$-16.28</td>
                <td>-100.20%</td>
                <td>2.0h</td>
                <td>STOP_LOSS</td>
            </tr>
            
            <tr class="profit">
                <td>9</td>
                <td>SHORT</td>
                <td>2025-03-29 08:00</td>
                <td>2025-03-29 09:00</td>
                <td>$83934.95</td>
                <td>$83544.80</td>
                <td>0.147125</td>
                <td>$+30.87</td>
                <td>$0.03</td>
                <td>$+30.84</td>
                <td>+199.80%</td>
                <td>1.0h</td>
                <td>TAKE_PROFIT</td>
            </tr>
            
            <tr class="profit">
                <td>10</td>
                <td>SHORT</td>
                <td>2025-03-29 09:00</td>
                <td>2025-03-29 10:00</td>
                <td>$83544.80</td>
                <td>$82927.81</td>
                <td>0.162579</td>
                <td>$+33.96</td>
                <td>$0.03</td>
                <td>$+33.92</td>
                <td>+199.80%</td>
                <td>1.0h</td>
                <td>TAKE_PROFIT</td>
            </tr>
            
            <tr class="profit">
                <td>11</td>
                <td>SHORT</td>
                <td>2025-03-29 10:00</td>
                <td>2025-03-29 11:00</td>
                <td>$82927.81</td>
                <td>$82293.56</td>
                <td>0.180151</td>
                <td>$+37.35</td>
                <td>$0.04</td>
                <td>$+37.31</td>
                <td>+199.80%</td>
                <td>1.0h</td>
                <td>TAKE_PROFIT</td>
            </tr>
            
            <tr class="loss">
                <td>12</td>
                <td>LONG</td>
                <td>2025-03-29 13:00</td>
                <td>2025-03-29 14:00</td>
                <td>$82851.95</td>
                <td>$82504.01</td>
                <td>0.198329</td>
                <td>$-20.54</td>
                <td>$0.04</td>
                <td>$-20.58</td>
                <td>-100.20%</td>
                <td>1.0h</td>
                <td>STOP_LOSS</td>
            </tr>
            
            <tr class="loss">
                <td>13</td>
                <td>LONG</td>
                <td>2025-03-29 14:00</td>
                <td>2025-03-29 16:00</td>
                <td>$82504.01</td>
                <td>$82198.43</td>
                <td>0.189188</td>
                <td>$-19.51</td>
                <td>$0.04</td>
                <td>$-19.55</td>
                <td>-100.20%</td>
                <td>2.0h</td>
                <td>STOP_LOSS</td>
            </tr>
            
            <tr class="loss">
                <td>14</td>
                <td>SHORT</td>
                <td>2025-03-29 16:00</td>
                <td>2025-03-29 17:00</td>
                <td>$82198.43</td>
                <td>$82332.63</td>
                <td>0.180377</td>
                <td>$-18.53</td>
                <td>$0.04</td>
                <td>$-18.57</td>
                <td>-100.20%</td>
                <td>1.0h</td>
                <td>STOP_LOSS</td>
            </tr>
            
            <tr class="loss">
                <td>15</td>
                <td>LONG</td>
                <td>2025-03-29 19:00</td>
                <td>2025-03-29 20:00</td>
                <td>$82758.40</td>
                <td>$82511.17</td>
                <td>0.170181</td>
                <td>$-17.60</td>
                <td>$0.04</td>
                <td>$-17.64</td>
                <td>-100.20%</td>
                <td>1.0h</td>
                <td>STOP_LOSS</td>
            </tr>
            
            <tr class="profit">
                <td>16</td>
                <td>LONG</td>
                <td>2025-03-29 22:00</td>
                <td>2025-03-29 23:00</td>
                <td>$82389.75</td>
                <td>$82648.54</td>
                <td>0.162378</td>
                <td>$+33.45</td>
                <td>$0.03</td>
                <td>$+33.41</td>
                <td>+199.80%</td>
                <td>1.0h</td>
                <td>TAKE_PROFIT</td>
            </tr>
            
            <tr class="loss">
                <td>17</td>
                <td>SHORT</td>
                <td>2025-03-29 23:00</td>
                <td>2025-03-30 00:00</td>
                <td>$82648.54</td>
                <td>$82799.73</td>
                <td>0.178041</td>
                <td>$-18.39</td>
                <td>$0.04</td>
                <td>$-18.43</td>
                <td>-100.20%</td>
                <td>1.0h</td>
                <td>STOP_LOSS</td>
            </tr>
            
            <tr class="profit">
                <td>18</td>
                <td>LONG</td>
                <td>2025-03-30 01:00</td>
                <td>2025-03-30 02:00</td>
                <td>$82941.13</td>
                <td>$83330.00</td>
                <td>0.168524</td>
                <td>$+34.94</td>
                <td>$0.03</td>
                <td>$+34.91</td>
                <td>+199.80%</td>
                <td>1.0h</td>
                <td>TAKE_PROFIT</td>
            </tr>
            
            <tr class="loss">
                <td>19</td>
                <td>LONG</td>
                <td>2025-03-30 02:00</td>
                <td>2025-03-30 03:00</td>
                <td>$83330.00</td>
                <td>$83088.81</td>
                <td>0.184495</td>
                <td>$-19.22</td>
                <td>$0.04</td>
                <td>$-19.26</td>
                <td>-100.20%</td>
                <td>1.0h</td>
                <td>STOP_LOSS</td>
            </tr>
            
            <tr class="loss">
                <td>20</td>
                <td>LONG</td>
                <td>2025-03-30 03:00</td>
                <td>2025-03-30 05:00</td>
                <td>$83088.81</td>
                <td>$82958.30</td>
                <td>0.175760</td>
                <td>$-18.25</td>
                <td>$0.04</td>
                <td>$-18.29</td>
                <td>-100.20%</td>
                <td>2.0h</td>
                <td>STOP_LOSS</td>
            </tr>
            
                    </tbody>
                </table>
            </div>
            <p class="note">Showing first 20 of 326 trades</p>
        </div>
        
                
        <div class="section">
            <h2>📈 Equity Curve & Drawdown Analysis (Best Cycle)</h2>
            <div class="chart-grid">
                <div class="chart-container" id="equity-curve"></div>
                <div class="chart-container" id="drawdown-chart"></div>
            </div>
            <div class="equity-stats">
                <div class="stat-card">
                    <h4>Equity Statistics</h4>
                    <p><strong>Starting Balance:</strong> $300.00</p>
                    <p><strong>Final Balance:</strong> $15077.59</p>
                    <p><strong>Peak Balance:</strong> $619035.83</p>
                    <p><strong>Total Return:</strong> +4925.86%</p>
                </div>
                <div class="stat-card">
                    <h4>Drawdown Statistics</h4>
                    <p><strong>Max Drawdown:</strong> 98.91%</p>
                    <p><strong>Avg Drawdown:</strong> 35.97%</p>
                    <p><strong>Recovery Factor:</strong> 49.80</p>
                </div>
            </div>
        </div>
        
                
        <div class="section">
            <h2>💰 Commission Analysis (0.1% of Trade Size)</h2>
            <div class="commission-grid">
                <div class="commission-card">
                    <h3>Commission Details</h3>
                    <p><strong>Total Commission Paid:</strong> $126.97</p>
                    <p><strong>Commission as % of Initial Capital:</strong> 42.32%</p>
                    <p><strong>Commission Rate:</strong> 0.1% per trade (entry + exit = 0.2% total)</p>
                    <p><strong>Number of Trades:</strong> 326</p>
                </div>
                <div class="commission-card">
                    <h3>Impact Analysis</h3>
                    <p><strong>Gross Return:</strong> +4968.19%</p>
                    <p><strong>Net Return (after commission):</strong> +4925.86%</p>
                    <p><strong>Commission Impact:</strong> -42.32%</p>
                    <p><strong>Avg Commission per Trade:</strong> $0.39</p>
                </div>
            </div>
        </div>
        
                
        <div class="section">
            <h2>🚀 Live Trading Readiness Assessment</h2>
            <div class="readiness-overview">
                <div class="readiness-card">
                    <h3>Overall Assessment</h3>
                    <div class="readiness-score">
                        <span class="score-value">100/100</span>
                        <span class="score-label">Stability Score</span>
                    </div>
                    <p><strong>Recommendation:</strong> READY FOR LIVE TRADING</p>
                    <p><strong>Consistency Rating:</strong> Excellent</p>
                    <p><strong>Risk Assessment:</strong> Low</p>
                </div>

                <div class="readiness-card">
                    <h3>Detailed Metrics</h3>
                    <p><strong>Composite Score Mean:</strong> 0.796</p>
                    <p><strong>Composite Score Std:</strong> 0.000</p>
                    <p><strong>Return Mean:</strong> +4925.86%</p>
                    <p><strong>Return Std:</strong> 0.00%</p>
                    <p><strong>Positive Return Ratio:</strong> 100.0%</p>
                    <p><strong>Total Cycles Analyzed:</strong> 1</p>
                </div>
            </div>

            <div class="readiness-criteria">
                <h3>Live Trading Criteria Analysis</h3>
                <div class="criteria-grid">
                    <div class="criteria-item">
                        <h4>Stability (Score ≥ 70)</h4>
                        <div class="criteria-status met">
                            100/100 - ✅ MET
                        </div>
                    </div>
                    <div class="criteria-item">
                        <h4>Consistency (Good/Excellent)</h4>
                        <div class="criteria-status met">
                            Excellent - ✅ MET
                        </div>
                    </div>
                    <div class="criteria-item">
                        <h4>Risk Level (Low/Moderate)</h4>
                        <div class="criteria-status met">
                            Low - ✅ MET
                        </div>
                    </div>
                    <div class="criteria-item">
                        <h4>Positive Return Rate (≥ 50%)</h4>
                        <div class="criteria-status met">
                            100.0% - ✅ MET
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
                
        <div class="section">
            <h2>🎯 Key Insights & Recommendations</h2>
            <div style="background: #e8f4fd; padding: 20px; border-radius: 10px; border-left: 5px solid #3498db;">
                <h3>Performance Analysis:</h3>
                <ul>
                    <li><strong>Success Rate:</strong> 100.0% of cycles completed successfully</li>
                    <li><strong>Profitability:</strong> 1/1 cycles were profitable (100.0%)</li>
                    <li><strong>Consistency:</strong> Composite score std deviation of 0.000</li>
                    <li><strong>Best Performance:</strong> Top cycle achieved 0.796 composite score</li>
                    <li><strong>Commission Impact:</strong> Average commission reduces returns by approximately 0.1-0.3% per cycle</li>
                </ul>

                <h3>Trading Pattern Analysis:</h3>
                <ul>
                    <li><strong>Activity Level:</strong> Average 326.0 trades per 30-day test period</li>
                    <li><strong>Trade Frequency:</strong> Approximately 10.9 trades per day</li>
                    <li><strong>Consistency:</strong> Trade count std deviation of 0.0</li>
                </ul>

                <h3>Composite Metrics Performance:</h3>
                <ul>
                    <li><strong>Excellent Cycles (0.8+):</strong> 0.0% - These represent the model's peak potential</li>
                    <li><strong>Good Cycles (0.6-0.8):</strong> 100.0% - Solid performance with room for improvement</li>
                    <li><strong>Acceptable Cycles (0.4-0.6):</strong> 0.0% - Moderate performance, needs optimization</li>
                    <li><strong>Poor Cycles (<0.4):</strong> 0.0% - Requires significant improvement</li>
                </ul>

                <h3>Recommendations:</h3>
                <ul>
                    <li><strong>Model Optimization:</strong> Focus on improving cycles with composite scores below 0.4</li>
                    <li><strong>Pattern Analysis:</strong> Study top-performing cycles for common characteristics</li>
                    <li><strong>Risk Management:</strong> Implement dynamic position sizing based on market volatility</li>
                    <li><strong>Commission Optimization:</strong> Consider trade frequency vs commission cost trade-offs</li>
                    <li><strong>Ensemble Methods:</strong> Combine multiple best-performing models for improved consistency</li>
                    <li><strong>Market Conditions:</strong> Analyze performance correlation with different market regimes</li>
                </ul>
            </div>
        </div>
        
            </div>

            <script>
                
        // Composite Score Distribution
        var compositeData = [{
            x: [0.7959944744333985],
            type: 'histogram',
            nbinsx: 30,
            name: 'Composite Scores',
            marker: {color: '#3498db'}
        }];

        Plotly.newPlot('composite-distribution', compositeData, {
            title: 'Distribution of Composite Scores (1000 Cycles)',
            xaxis: {title: 'Composite Score'},
            yaxis: {title: 'Frequency'}
        });

        // Returns Distribution
        var returnsData = [{
            x: [4925.863721285559],
            type: 'histogram',
            nbinsx: 30,
            name: 'Returns',
            marker: {color: '#e74c3c'}
        }];

        Plotly.newPlot('returns-distribution', returnsData, {
            title: 'Distribution of Returns (1000 Cycles)',
            xaxis: {title: 'Return (%)'},
            yaxis: {title: 'Frequency'}
        });

        // Performance Categories Pie Chart
        var categoryData = [{
            values: [0,
                    1,
                    0,
                    0],
            labels: ['Excellent (0.8+)', 'Good (0.6-0.8)', 'Acceptable (0.4-0.6)', 'Poor (<0.4)'],
            type: 'pie',
            marker: {colors: ['#27ae60', '#f39c12', '#e67e22', '#e74c3c']}
        }];

        Plotly.newPlot('performance-categories', categoryData, {
            title: 'Performance Category Distribution'
        });

        // Equity Curve
        var equityData = [{
            y: [300.0, 300.0, 12300.0, 12318.348154263052, 13528.770000000002, 13529.349677298434, 14880.294123000001, 14134.791387437699, 14125.655197069691, 14121.167902568768, 327.47898387627004, 13426.63833892707, 12753.963758146827, 12740.164654587124, 12114.99017386367, 13325.27769223265, 13333.8827576457, 12657.681279851791, 13922.183639708986, 15313.009785315911, 410.7994990943652, 410.7994990943652, 16842.779462868973, 15998.956211779241, 15981.970946738498, 15197.408505569098, 352.0980082790265, 352.0980082790265, 14436.018339440085, 334.4578980642473, 334.4578980642473, 13712.773820634136, 15082.67992531549, 349.4399429526142, 14327.037661057184, 15758.308723396793, 14968.817456354614, 14972.508425226366, 14218.87970179125, 15639.345784000197, 15657.973750133973, 14855.814560221788, 14877.409258837444, 344.1838597745043, 344.1838597745043, 14111.538250754678, 15521.280922005068, 14743.664747812612, 14756.469183774052, 395.5257769785145, 16216.556856119096, 375.7099355518909, 15404.107357627528, 15403.029593905208, 16942.97768265452, 392.5398658720372, 16094.134500753522, 15287.818362265774, 16815.071416656127, 16808.920038927383, 15972.636338681654, 17568.30270891595, 19323.376149536656, 18355.275004444866, 425.26038357858977, 17435.67572672218, 467.7438958980909, 19177.499731821725, 19184.63763071179, 21093.33195503072, 20036.55602408368, 19032.724567277084, 19050.6038847332, 20934.093751548065, 485.00721108769534, 19885.295654595506, 21871.836690489607, 21879.845477734565, 21845.51508019696, 21885.364942417444, 21849.132619637498, 20776.057672296072, 20763.035448344453, 19735.177182914038, 19766.95643098317, 19744.444971793924, 19756.801251318033, 457.23036112317186, 18746.444806050047, 17807.24792126694, 412.5635317173528, 16915.10480041147, 16067.658049910853, 17672.81708909694, 17672.204664222267, 409.4489988520289, 16787.408952933187, 15946.359764391234, 15946.75089328708, 15945.137029665435, 15973.406144768553, 369.4499302486642, 15147.447140195232, 350.9404887432061, 14388.560038471453, 14420.51454286544, 14390.379887470728, 15825.97718631475, 15033.095729280381, 15042.983213454203, 348.29116178642516, 14279.937633243431, 13564.512757817936, 12884.930668651257, 14172.135242449518, 13462.1112668028, 14806.976182356402, 343.0523579419596, 343.0523579419596, 14065.14667562034, 325.86543480906744, 13360.482827171767, 14695.195061606222, 16163.245048260686, 17777.95322858193, 19553.97075611727, 18574.316821235792, 20429.891071677248, 473.3256958289321, 19406.353528986216, 520.6109328422424, 21345.04824653194, 20275.661329380688, 20257.930713758193, 20308.885797142095, 543.93170478502, 543.93170478502, 22301.19989618582, 21183.909781386905, 490.7950219838884, 20122.59590133942, 22132.843231883235, 593.7540066036187, 24343.914270748366, 26775.87130639613, 29450.7808499051, 29450.9739667311, 27975.296729324855, 27941.807685479114, 750.4885090874246, 750.4885090874246, 30770.02887258441, 712.8890347821447, 29228.450426067928, 27764.10505972193, 744.8229062241012, 30537.73915518815, 30502.41294583729, 819.2307145558889, 33588.45929679144, 901.0718629400221, 36943.9463805409, 37010.29284252388, 855.928162606727, 855.928162606727, 35093.0546668758, 33334.89262806533, 31664.814507399256, 30078.40730057855, 33083.24018990635, 887.5184362165363, 36388.255884877995, 36396.760182330385, 36461.8653978667, 36471.07134212679, 36416.52041622198, 36345.98800489597, 34565.2042650456, 34642.58752792832, 34641.27546969252, 32833.487531366816, 32812.902107075795, 32808.71913660207, 32889.685663070115, 32871.28365743296, 32873.34449819749, 31188.52980604534, 31261.758425006585, 836.6893642358358, 34304.263933669274, 794.7712270876204, 32585.620310592443, 35840.92377962062, 830.3730121527227, 34045.293498261635, 788.7713242438713, 788.7713242438713, 788.7713242438713, 32339.62429399872, 30719.409116869385, 29180.366720114234, 29184.03699474545, 29217.519305099842, 782.8167159866742, 32095.485355453646, 32154.038715143495, 32135.261064086073, 743.5975985157419, 30487.50153914542, 30484.16979039604, 30469.52084347535, 30542.926791727958, 30548.035473143682, 33533.20294290605, 36883.16991690236, 989.4585022341685, 989.4585022341685, 40567.798591600906, 44620.52167090184, 49078.111785824934, 53981.015153228844, 59373.7185670364, 59434.39985667492, 56399.09526682788, 53573.50059395979, 53641.56142951191, 50889.46821420242, 55973.32608880124, 61565.06136507247, 1426.35736074835, 58480.65179068235, 1568.85046108711, 1568.85046108711, 64322.868904571515, 64316.55786551562, 61100.293172452475, 67204.21246038048, 63837.28141611542, 60639.03361716804, 66696.87307552314, 63355.35973443942, 69684.56017190992, 1614.472285543835, 1614.472285543835, 1614.472285543835, 66193.36370729723, 62877.07618556165, 1456.7545041137805, 1456.7545041137805, 59726.934668664995, 56734.615241764885, 62402.4033044172, 62369.62034446076, 68636.40339452848, 1841.2970754546795, 75493.18009364186, 75448.03259708772, 83034.9487849967, 2227.564394356533, 91330.14016861786, 2115.9634181992706, 86754.5001461701, 86820.13270792206, 82408.09968884698, 82389.57777055776, 90640.66884776279, 90714.07273286596, 90816.97067824558, 99695.67166565428, 2674.518762562272, 109655.26926505315, 104161.54027487399, 114567.27814833392, 2654.3282320268872, 2654.3282320268872, 2654.3282320268872, 108827.45751310239, 119699.32051866132, 113702.3845606764, 113566.22191885574, 108005.8950941865, 2502.312189023604, 2502.312189023604, 102594.79974996777, 112844.02024498953, 124117.13786746399, 2875.582177080586, 117898.86926030404, 2731.5155100088487, 111992.13591036279, 3004.3939094587327, 123180.15028780805, 3304.5328610136603, 3304.5328610136603, 3304.5328610136603, 135485.8473015601, 128698.0063517519, 122250.23623352914, 122400.21827128374, 122471.60311717908, 3279.5862154453343, 134463.03483325872, 134684.98669145268, 134682.8914479978, 127726.43678811248, 127796.41198968621, 121327.34230502804, 115248.84245554611, 115408.3010366236, 115172.39969492091, 109474.87544852325, 2936.863792825139, 120411.41550583071, 132440.5159148632, 125805.24606752853, 126074.55285884536, 138373.19014967466, 152196.67184562713, 4082.9541308050075, 167401.1193630053, 167223.91452318372, 159014.32328291875, 151047.70568644453, 143480.21563155364, 157813.88917314584, 157727.88234670594, 157865.29139293358, 149907.41332557128, 3473.09882726732, 142397.0519179601, 3820.061400111325, 156622.51740456434, 172269.1068932803, 172240.15080454605, 4621.4339188272925, 189478.790671919, 208407.72186004373, 229227.6532738621, 217743.3478448416, 239495.90829454127, 6424.915842272339, 263421.5495331659, 250224.1299015543, 275221.52047871955, 275248.8814509816, 275072.6124108557, 6376.412739091114, 261432.92230273574, 261951.1986830885, 287550.07124077895, 6662.044211502828, 6662.044211502828, 273143.812671616, 273526.22909531015, 273700.5524205347, 7327.582428231961, 7327.582428231961, 7327.582428231961, 300430.87955751043, 330443.9244253057, 313888.6838115979, 298162.86075263686, 297814.0360911757, 6907.924425095846, 283224.9014289297, 269035.33386734035, 6233.089357087478, 255556.6636405866, 255383.96378649338, 256023.28339523438, 255333.9155966674, 255457.9404453606, 255838.02036447727, 242753.2747921932, 242461.08246173745, 242721.67659860273, 230591.3357251043, 5342.407556226259, 5342.407556226259, 219038.70980527665, 219437.73067115952, 5876.114071093262, 5876.114071093262, 5876.114071093262, 5876.114071093262, 240920.67691482374, 5581.72075613149, 228850.55100139105, 251712.7210464301, 239101.91372200393, 239400.86273735802, 227122.90784453155, 215744.05016152048, 4998.421298742154, 204935.27324842833, 5497.763586486495, 5497.763586486495, 5497.763586486495, 225408.30704594628, 225535.79378550535, 225422.8160405769, 225897.14055679872, 214115.35086294438, 5744.035961320793, 235505.4744141525, 236049.82426330147, 5456.259759658621, 223706.65014600346, 212498.9469736887, 4923.237798300168, 201852.74973030685, 4676.58358460533, 191739.92696881853, 191605.00921581828, 4442.286747016603, 182133.75662768073, 181941.1979771318, 4219.728180991071, 173008.85542063392, 173008.85542063392, 164341.11176406016, 3807.502977187335, 3807.502977187335, 156107.62206468073, 3616.7470780302497, 148286.63019924026, 148511.49131484504, 163100.46455614435, 4375.468316226907, 179394.20096530317, 170406.55149694148, 3948.028860169383, 3948.028860169383, 161869.1832669447, 161975.7991181885, 178039.91467531244, 169120.1149500793, 3918.224321733667, 3918.224321733667, 160647.19719108034, 176695.85219046925, 177095.73624897996, 4740.189459129198, 4740.189459129198, 194347.76782429716, 184610.94465629986, 4277.120398268762, 4277.120398268762, 175361.93632901923, 166576.3033189354, 183217.27602049703, 183509.25315869902, 4244.831475411467, 4244.831475411467, 174038.09049187016, 174081.3711122118, 4032.1654184933527, 4032.1654184933527, 4032.1654184933527, 165318.78215822746, 181834.1284958344, 199999.3579325682, 199911.39208380374, 219979.29379003178, 219763.70345648154, 5096.544662711005, 5096.544662711005, 5096.544662711005, 208958.33117115122, 229833.26845514923, 5324.844431842591, 5324.844431842591, 218318.62170554622, 218578.94199236349, 240128.65201393032, 240040.51453676866, 240318.04910445047, 228098.20654803238, 250885.21738218082, 275948.6505986607, 262123.62320366778, 248991.22968116403, 6679.645207958837, 273865.4535263123, 260144.7943046441, 259993.3808329651, 286133.2592556781, 285877.51526761893, 271797.98296696856, 6297.095220007888, 258180.90402032345, 258503.089966773, 258391.48753458963, 258213.303768464, 245246.04072890524, 245447.42551679525, 245409.10419033177, 245220.52143849476, 245199.72166247922, 232959.21408838706, 233033.54050597895, 221287.95746255887, 221228.01127761876, 221036.80054139334, 221068.72424345417, 5126.864165699626, 5126.864165699626, 5126.864165699626, 210201.4307936847, 210070.24330725675, 209966.73291476947, 210212.62765655736, 209970.02896098307, 199670.33911092105, 199758.01399411427, 199510.37049842463, 189666.8551214639, 189880.53887608874, 208614.5739480981, 208475.51743011293, 208853.7636068531, 198162.98379329845, 197949.18477029895, 188235.01830525417, 188403.1488471396, 188473.31085002574, 188305.37400692128, 4361.083997272218, 178804.44388816092, 4142.59368900888, 169846.34124936405, 4556.438798540867, 186813.9907401755, 186687.50481288484, 186923.60902307543, 187231.16525988578, 187060.62652529028, 186669.22439624812, 186863.97290978523, 187189.68112939174, 186989.08887611015, 186865.72817227256, 186900.66217689915, 186693.90509927165, 177454.60980409276, 177803.9177652882, 4760.544520085893, 4760.544520085893, 195182.32532352163, 195059.72652990784, 195592.9408033133, 195479.05059559277, 214681.03962334144, 236127.67548171326, 224297.67894007938, 224354.9177857462, 224365.7810099577, 224174.82104721118, 213060.36522518145, 213349.25353542424, 234345.09571117707, 222604.4064160471, 244842.58661701015, 232575.97302749797, 232427.9672473036, 232803.4402882077, 232425.03000579288, 220923.91677882028, 220854.3200981518, 209855.6285482014, 210170.57362165497, 210045.93030373726, 209652.0265100097, 4861.996623364305, 4861.996623364305, 199341.8615579365, 189354.8342938839, 189697.40197566772, 208271.38223984287, 4825.292341210408, 4825.292341210408, 197836.98598962676, 197941.158972399, 198146.32543189867, 217600.90088999044, 239339.2308889005, 239855.82760428337, 263249.22005470167, 289547.8171381664, 289510.45961876144, 275041.4714995442, 261261.89377741708, 261679.4754070686, 261015.16852202738, 261195.39676491957, 6052.992021930938, 6052.992021930938, 248172.67289916845, 6657.685924921839, 6657.685924921839, 272965.12292179535, 273366.58848680597, 6324.135860083255, 259289.57026341342, 258990.03989691075, 259237.95095704423, 285192.59833272843, 313683.338906168, 8415.129377143761, 345020.3044628942, 327734.7872093032, 360475.4924515126, 342415.67027969175, 9185.926725381292, 376622.99574063293, 377358.8035939978, 376771.51461533783, 357754.1836540273, 358317.3970121505, 339830.6990529605, 340336.83053423953, 373779.78588835127, 355053.41861534485, 337265.242342716, 336981.6933772598, 337884.4798912783, 9047.757074457399, 370958.0400527534, 408016.7482540235, 387575.1091664969, 388149.86772619525, 387525.04224439536, 10397.41128224951, 426293.86257222993, 404936.5400573611, 405494.9556009799, 384649.2194004875, 10318.918937038929, 423075.67641859606, 423832.4614781307, 465340.9364928138, 442027.35557452386, 419881.7850602402, 461827.9753877583, 462188.4678065658, 462524.8916681506, 12389.380247048666, 12389.380247048666, 12389.380247048666, 507964.5901289953, 482515.56416353263, 530718.8690234696, 12295.850089887652, 12295.850089887652, 504129.8536853938, 503935.31230306695, 504326.3963055464, 554492.4260685645, 554007.813526315, 12846.64281762267, 526712.3555225295, 579330.9198392301, 550306.4407552846, 522736.0880734449, 496547.0100609653, 546152.0563660559, 546676.237878509, 545901.1311022084, 546937.5615398391, 518789.83834211633, 519957.7919590812, 520050.2099392129, 12019.474815638447, 12019.474815638447, 492798.46744117636, 13220.220349720728, 542029.0343385498, 596177.734868971, 13812.420252488671, 566309.2303520356, 13120.41799783899, 537937.1379113985, 14431.147755823105, 591677.0579887474, 591597.226806866, 13708.147253256368, 562034.0373835112, 561515.0426424172, 561492.132168263, 561840.7773838101, 563303.9736660607, 618181.2377181238, 618620.2067868962, 619035.826437117, 618384.345674019, 619002.9453381153, 618422.0245987504],
            type: 'scatter',
            mode: 'lines',
            name: 'Equity Curve',
            line: {color: '#2ecc71', width: 2}
        }];

        Plotly.newPlot('equity-curve', equityData, {
            title: 'Equity Curve (Best Cycle)',
            xaxis: {title: 'Time (Hours)'},
            yaxis: {title: 'Balance ($)'}
        });

        // Drawdown Chart
        var drawdownData = [{
            y: [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 5.010000000000016, 5.0713979152057815, 5.101553868198586, 97.79924387804878, 9.768999000000015, 14.289572150099996, 14.382306228107053, 18.583664585379985, 10.450172677459456, 10.392344079839534, 14.936619026318759, 6.438787267047993, 0.0, 97.31731707317074, 97.31731707317074, 0.0, 5.009999999999981, 5.110845974253744, 9.768999000000003, 97.90950175975853, 97.90950175975853, 14.28957215010001, 98.01423572159463, 98.01423572159463, 18.58366458538, 10.450172677459443, 97.92528339088584, 14.936619026318724, 6.438787267047988, 11.126204024968876, 11.104289774533617, 15.578781203317929, 7.145101445529376, 7.034502323960147, 11.797131863108351, 11.668918472537852, 97.9564901355309, 97.9564901355309, 16.216095556766614, 7.846083502887616, 12.462994719392954, 12.386971424130627, 97.65165970467953, 3.71804789186029, 97.76931155347506, 8.541773692478092, 8.548172658424871, 0.0, 97.68317073170732, 5.010000000000041, 9.768999000000015, 0.7549220001000099, 0.7912283557121121, 5.727100407895004, 0.0, 0.0, 5.01000000000002, 97.7992438780488, 9.768999000000008, 97.57938834146587, 0.7549220001000153, 0.717982808755669, 0.0, 5.010000000000014, 9.768999000000022, 9.684236111451968, 0.7549220001000225, 97.70066098555841, 5.727100407895019, 0.0, 0.0, 0.1569042047053726, 0.0, 0.16555503129729357, 5.068717259410882, 5.1282192324685925, 9.824774524714401, 9.679566765315617, 9.782427554927649, 9.725968457459459, 97.91079398343966, 14.3425533210262, 18.63399139964279, 98.11488849830539, 22.71042843052066, 26.582635966151592, 19.24824129917016, 19.251039629818457, 98.12911961975809, 23.293904410081726, 27.136879799136633, 27.135092628135027, 27.142466796333238, 27.013297759502013, 98.31188590539512, 30.78732212119989, 98.39646042153483, 34.25487728292777, 34.1088687311944, 34.246561913254645, 27.686939523492253, 31.309823853365295, 31.26464533246863, 98.40856589459295, 34.7512016783117, 38.02016647422828, 41.12535613386945, 35.24377921164301, 38.48806587313968, 32.34302365386632, 98.43250336997092, 98.43250336997092, 35.73263816880765, 98.51103495113537, 38.95243299655036, 32.85378105290576, 26.14587378009104, 18.767846570722124, 10.652754443137242, 15.129051445536074, 6.650443684945127, 97.83725015747146, 11.327256456329387, 97.62119144820285, 2.4688493763166854, 7.35516002256323, 7.436175877995139, 7.203348673523248, 97.51463269533701, 97.51463269533701, 0.0, 5.010000000000021, 97.79924387804878, 9.76899900000002, 0.7549220000999961, 97.3375692367783, 0.0, 0.0, 0.0, 0.0, 5.01062287132923, 5.124334030367877, 97.45173619746768, 97.45173619746768, 0.0, 97.68317073170732, 5.010000000000014, 9.768998999999999, 97.57938834146586, 0.7549220001000038, 0.8697292025798544, 97.3375692367783, 0.0, 97.31731707317073, 0.0, 0.0, 97.68732399322363, 97.68732399322363, 5.180283722168277, 9.930751507687628, 14.443220857152486, 18.729615492209156, 10.610704079880849, 97.60196861993806, 1.6807134174609364, 1.657735221939559, 1.4818241157688186, 1.4569501049111955, 1.6043440370179074, 1.7949191606088617, 6.606509675246168, 6.397423886025363, 6.400969003167195, 11.28552354051633, 11.341144349512925, 11.35244653102098, 11.133678938955304, 11.183400257604285, 11.177831966714786, 15.730118811136462, 15.53225866646582, 97.73930628488705, 7.311557680368979, 97.85256704001421, 11.955248640582493, 3.1595779797767016, 97.7563727590973, 8.011283122989871, 97.86877848386655, 97.86877848386655, 97.86877848386655, 12.619917838528098, 16.997659954817838, 21.156077191081447, 21.146160288648844, 21.055692724674525, 97.88486754396271, 13.279569302470486, 13.121360990153184, 13.17209728434388, 97.99083568001016, 17.624262880416705, 17.63326510248384, 17.67284584015328, 17.474506560415765, 17.460703153246136, 9.394926742170336, 0.34347992371315317, 97.32653155044126, 97.32653155044126, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 5.106982819994198, 9.861122980712505, 9.746608767199378, 14.377080719378787, 5.823351083244732, 0.0, 97.68317073170732, 5.009999999999983, 97.45171948780488, 97.45171948780488, 0.0, 0.009811501202250536, 5.0100000000000104, 0.0, 5.010000000000008, 9.768999, 0.7549220000999838, 5.727100407894984, 0.0, 97.68317073170732, 97.68317073170732, 97.68317073170732, 5.01, 9.768998999999988, 97.90950175975854, 97.90950175975854, 14.289572150100014, 18.583664585379996, 10.450172677459447, 10.497217474578878, 1.5041449279376542, 97.35766851234727, 0.0, 0.05980341071622755, 0.0, 97.31731707317073, 0.0, 97.68317073170732, 5.01, 4.938137018479565, 9.768998999999999, 9.789279181608197, 0.7549220001000044, 0.6745499728944795, 0.5618840499147872, 0.0, 97.31731707317073, 0.0, 5.010000000000003, 0.0, 97.68317073170732, 97.68317073170732, 97.68317073170732, 5.010000000000002, 0.0, 5.009999999999995, 5.123753897040229, 9.768998999999996, 97.90950175975853, 97.90950175975853, 14.289572150099985, 5.727100407894995, 0.0, 97.68317073170732, 5.01, 97.79924387804878, 9.768999000000013, 97.57938834146586, 0.7549220001000014, 97.3375692367783, 97.3375692367783, 97.3375692367783, 0.0, 5.010000000000031, 9.768999000000022, 9.658299586930868, 9.605611540675781, 97.57938834146586, 0.7549220001000141, 0.5911027801486124, 0.5926492468066525, 5.727100407894986, 5.675452798224004, 10.450172677459449, 14.93661902631875, 14.818925123779565, 14.993040240894068, 19.198294413100182, 97.83234643963338, 11.126204024968898, 2.2477118070632827, 7.1451014455294395, 6.94633028479158, 0.0, 0.0, 97.31731707317073, 0.0, 0.10585642467379225, 5.009999999999994, 9.768998999999985, 14.289572150099996, 5.7271004078949925, 5.778478096865756, 5.6963943887337525, 10.450172677459427, 97.92528339088582, 14.936619026318738, 97.71801920163531, 6.438787267047974, 0.0, 0.01680863693812254, 97.31731707317073, 0.0, 0.0, 0.0, 5.009999999999999, 0.0, 97.31731707317073, 0.0, 5.00999999999999, 0.0, 0.0, 0.06403987518375408, 97.68340103491877, 5.0194424316693596, 4.831148703600182, 0.0, 97.68317073170732, 97.68317073170732, 5.009999999999984, 4.87700875362538, 4.816385111811499, 97.45171948780488, 97.45171948780488, 97.45171948780488, 0.0, 0.0, 5.009999999999998, 9.768998999999987, 9.874561437580835, 97.90950175975853, 14.289572150100001, 18.583664585379985, 98.11372251194274, 22.662622989652444, 22.714885973271702, 22.52141302325367, 22.730031716959694, 22.692498919554225, 22.577477915679612, 26.53722557787086, 26.62564975784752, 26.54678792453692, 30.21771057641954, 98.38326349454978, 98.38326349454978, 33.713803276540894, 33.59305030262048, 98.22175151765529, 98.22175151765529, 98.22175151765529, 98.22175151765529, 27.091812223867347, 98.31084176662075, 30.744512431451597, 23.82588922335359, 27.64221217326358, 27.551743263637228, 31.267337343383065, 34.71084374247958, 98.48736171880441, 37.98183047098135, 98.33624915451298, 98.33624915451298, 98.33624915451298, 31.786215335032402, 31.74763488911233, 31.78182457655323, 31.638282970501088, 35.20372594674728, 98.26172141875188, 28.730578168827336, 28.565845271983893, 98.34880917567243, 32.30117620256909, 35.69288727482037, 98.51011399078907, 38.914673622351884, 98.58475727985055, 41.97504847387204, 42.015877716907724, 98.65566094013003, 44.88209854533105, 44.94037126161229, 98.72301232702951, 47.64350540820996, 47.64350540820996, 50.26656578725864, 98.84776124003211, 98.84776124003211, 52.75821084131699, 98.90548840190651, 55.125024478167006, 55.05697628633056, 50.64201442353588, 98.67588174791334, 45.71115166444712, 48.43102296605832, 98.8052348467185, 98.8052348467185, 51.01462871545881, 50.98236428468459, 46.12099012413315, 48.820328518914074, 98.81425439171015, 98.81425439171015, 51.38443006011647, 46.52773462312212, 46.406720427080785, 98.56550866614566, 98.56550866614566, 41.185855311972006, 44.13244396084221, 98.70564411020499, 98.70564411020499, 46.93140851840402, 49.59014495163197, 44.55420043230001, 44.465841374493216, 98.71541548757662, 98.71541548757662, 47.332034990641766, 47.31893727053179, 98.77977317164904, 98.77977317164904, 98.77977317164904, 49.97070003761061, 44.97277297136792, 39.47555299120758, 39.50217349842903, 33.429160735029214, 33.49440337307293, 98.45766731176109, 98.45766731176109, 98.45766731176109, 36.764359782204245, 30.447119324446447, 98.38857850356808, 98.38857850356808, 33.931718646291685, 33.85293968636074, 27.33149733905622, 27.35816978501356, 27.274181384208646, 30.97218932236951, 24.076311035674223, 16.491534508138077, 20.675308629280362, 24.649475666953418, 97.97858434844103, 17.121958286082062, 21.274148175949335, 21.3199694062662, 13.409435578726661, 13.48682964445323, 17.74762285623248, 98.09435285246671, 21.868466951135225, 21.770966006910005, 21.804739492797953, 21.85866203546099, 25.782856756883355, 25.721913046618365, 25.733509969312625, 25.790579486376675, 25.796873981290357, 29.501135633363507, 29.478642734539246, 33.03312873813199, 33.05126984484, 33.10913465096647, 33.09947379788335, 98.44849192654516, 98.44849192654516, 98.44849192654516, 36.38816898835157, 36.4278693661558, 36.45919401304325, 36.384780557805556, 36.4581965529691, 39.57512172203517, 39.5485892677479, 39.62353193649883, 42.602408123761215, 42.53774246074548, 36.86838869532497, 36.91047042469161, 36.79600435381498, 40.03128242168917, 40.09598296759006, 43.03571517236255, 42.98483496865542, 42.96360231836289, 43.01442390432381, 98.68023477663968, 45.88962584222719, 98.74635501433004, 48.60055558753161, 98.62111588026161, 43.46575109072602, 43.50402866762826, 43.432578054456535, 43.33950439987346, 43.39111337858055, 43.50956074592824, 43.45062532628758, 43.352058460465194, 43.41276233136566, 43.4500941431253, 43.43952230262699, 43.50209179243894, 46.29811696108063, 46.192408265784465, 98.55934875232907, 98.55934875232907, 40.93329884549259, 40.97040008553717, 40.809037072332195, 40.84350289218911, 35.0325354001573, 28.542285686633008, 32.12231717373271, 32.10499537071686, 32.10170790697232, 32.15949682322449, 35.52298908332869, 35.43556477654344, 29.081735692753224, 32.634740734546284, 25.904951333927478, 29.617113272097704, 29.661903255890497, 29.548276400272833, 29.662792133334932, 33.14329589716562, 33.1643574678359, 36.49281677271762, 36.39750708469678, 36.43522704524212, 36.55443147437867, 98.52864699152207, 98.52864699152207, 39.67452665240447, 42.696832867119, 42.593163936791534, 36.97224647054419, 98.5397545590822, 98.5397545590822, 40.12993692236993, 40.0984117602858, 40.036323628432115, 34.148917620914695, 27.570394491244066, 27.414060336733186, 20.334676900919348, 12.376111123321177, 12.387416375632878, 16.7660679560428, 20.93608795144505, 20.809718059676648, 21.010752739372016, 20.956211490594136, 98.16822414500189, 98.16822414500189, 24.89718994507766, 97.9852297370876, 97.9852297370876, 17.39441922059093, 17.27292642398139, 98.08616972725949, 21.532958817639315, 21.62360365761439, 21.5485800176535, 13.694101403521481, 5.072142133733277, 97.45338656421691, 0.0, 5.010000000000011, 0.0, 5.010000000000019, 97.45171948780488, 0.0, 0.0, 0.1556314502448504, 5.195219974532037, 5.045968558437034, 9.94493945380799, 9.810814722528741, 0.9484389052434036, 5.910922116090713, 10.624784918074583, 10.699925331589691, 10.460687103828665, 97.60234636418025, 1.6962009313902189, 0.0, 5.010000000000004, 4.869133586511379, 5.0222708007246775, 97.45171948780488, 0.0, 5.010000000000021, 4.879006900486614, 9.768998999999983, 97.57938834146586, 0.7549220001000108, 0.5773953861890666, 0.0, 5.009999999999996, 9.768999, 0.7549220000999793, 0.677453548361242, 0.60515733816311, 97.3375692367783, 97.3375692367783, 97.3375692367783, 0.0, 5.010000000000002, 0.0, 97.68317073170732, 97.68317073170732, 5.009999999999997, 5.046656202310048, 4.972966717103117, 0.0, 0.08739750436006344, 97.68317073170732, 5.009999999999999, 0.0, 5.010000000000014, 9.768999000000006, 14.289572150100005, 5.727100407894978, 5.636619907976453, 5.77041334964459, 5.5915120685048985, 10.450172677459461, 10.248568796677649, 10.232616259540956, 97.92528339088581, 97.92528339088581, 14.936619026318734, 97.71801920163531, 6.438787267047982, 0.0, 97.68317073170732, 5.009999999999999, 97.79924387804877, 9.768999000000012, 97.57938834146586, 0.7549220000999942, 0.7683125004847348, 97.70066098555841, 5.727100407894973, 5.814154101909228, 5.817996995196633, 5.759516915321828, 5.514087373647958, 0.0, 0.0, 0.0, 0.10524120499577758, 0.005311663331485876, 0.09915449351280785],
            type: 'scatter',
            mode: 'lines',
            fill: 'tozeroy',
            name: 'Drawdown',
            line: {color: '#e74c3c'},
            fillcolor: 'rgba(231, 76, 60, 0.3)'
        }];

        Plotly.newPlot('drawdown-chart', drawdownData, {
            title: 'Drawdown Chart (Best Cycle)',
            xaxis: {title: 'Time (Hours)'},
            yaxis: {title: 'Drawdown (%)', range: [Math.min(...[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 5.010000000000016, 5.0713979152057815, 5.101553868198586, 97.79924387804878, 9.768999000000015, 14.289572150099996, 14.382306228107053, 18.583664585379985, 10.450172677459456, 10.392344079839534, 14.936619026318759, 6.438787267047993, 0.0, 97.31731707317074, 97.31731707317074, 0.0, 5.009999999999981, 5.110845974253744, 9.768999000000003, 97.90950175975853, 97.90950175975853, 14.28957215010001, 98.01423572159463, 98.01423572159463, 18.58366458538, 10.450172677459443, 97.92528339088584, 14.936619026318724, 6.438787267047988, 11.126204024968876, 11.104289774533617, 15.578781203317929, 7.145101445529376, 7.034502323960147, 11.797131863108351, 11.668918472537852, 97.9564901355309, 97.9564901355309, 16.216095556766614, 7.846083502887616, 12.462994719392954, 12.386971424130627, 97.65165970467953, 3.71804789186029, 97.76931155347506, 8.541773692478092, 8.548172658424871, 0.0, 97.68317073170732, 5.010000000000041, 9.768999000000015, 0.7549220001000099, 0.7912283557121121, 5.727100407895004, 0.0, 0.0, 5.01000000000002, 97.7992438780488, 9.768999000000008, 97.57938834146587, 0.7549220001000153, 0.717982808755669, 0.0, 5.010000000000014, 9.768999000000022, 9.684236111451968, 0.7549220001000225, 97.70066098555841, 5.727100407895019, 0.0, 0.0, 0.1569042047053726, 0.0, 0.16555503129729357, 5.068717259410882, 5.1282192324685925, 9.824774524714401, 9.679566765315617, 9.782427554927649, 9.725968457459459, 97.91079398343966, 14.3425533210262, 18.63399139964279, 98.11488849830539, 22.71042843052066, 26.582635966151592, 19.24824129917016, 19.251039629818457, 98.12911961975809, 23.293904410081726, 27.136879799136633, 27.135092628135027, 27.142466796333238, 27.013297759502013, 98.31188590539512, 30.78732212119989, 98.39646042153483, 34.25487728292777, 34.1088687311944, 34.246561913254645, 27.686939523492253, 31.309823853365295, 31.26464533246863, 98.40856589459295, 34.7512016783117, 38.02016647422828, 41.12535613386945, 35.24377921164301, 38.48806587313968, 32.34302365386632, 98.43250336997092, 98.43250336997092, 35.73263816880765, 98.51103495113537, 38.95243299655036, 32.85378105290576, 26.14587378009104, 18.767846570722124, 10.652754443137242, 15.129051445536074, 6.650443684945127, 97.83725015747146, 11.327256456329387, 97.62119144820285, 2.4688493763166854, 7.35516002256323, 7.436175877995139, 7.203348673523248, 97.51463269533701, 97.51463269533701, 0.0, 5.010000000000021, 97.79924387804878, 9.76899900000002, 0.7549220000999961, 97.3375692367783, 0.0, 0.0, 0.0, 0.0, 5.01062287132923, 5.124334030367877, 97.45173619746768, 97.45173619746768, 0.0, 97.68317073170732, 5.010000000000014, 9.768998999999999, 97.57938834146586, 0.7549220001000038, 0.8697292025798544, 97.3375692367783, 0.0, 97.31731707317073, 0.0, 0.0, 97.68732399322363, 97.68732399322363, 5.180283722168277, 9.930751507687628, 14.443220857152486, 18.729615492209156, 10.610704079880849, 97.60196861993806, 1.6807134174609364, 1.657735221939559, 1.4818241157688186, 1.4569501049111955, 1.6043440370179074, 1.7949191606088617, 6.606509675246168, 6.397423886025363, 6.400969003167195, 11.28552354051633, 11.341144349512925, 11.35244653102098, 11.133678938955304, 11.183400257604285, 11.177831966714786, 15.730118811136462, 15.53225866646582, 97.73930628488705, 7.311557680368979, 97.85256704001421, 11.955248640582493, 3.1595779797767016, 97.7563727590973, 8.011283122989871, 97.86877848386655, 97.86877848386655, 97.86877848386655, 12.619917838528098, 16.997659954817838, 21.156077191081447, 21.146160288648844, 21.055692724674525, 97.88486754396271, 13.279569302470486, 13.121360990153184, 13.17209728434388, 97.99083568001016, 17.624262880416705, 17.63326510248384, 17.67284584015328, 17.474506560415765, 17.460703153246136, 9.394926742170336, 0.34347992371315317, 97.32653155044126, 97.32653155044126, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 5.106982819994198, 9.861122980712505, 9.746608767199378, 14.377080719378787, 5.823351083244732, 0.0, 97.68317073170732, 5.009999999999983, 97.45171948780488, 97.45171948780488, 0.0, 0.009811501202250536, 5.0100000000000104, 0.0, 5.010000000000008, 9.768999, 0.7549220000999838, 5.727100407894984, 0.0, 97.68317073170732, 97.68317073170732, 97.68317073170732, 5.01, 9.768998999999988, 97.90950175975854, 97.90950175975854, 14.289572150100014, 18.583664585379996, 10.450172677459447, 10.497217474578878, 1.5041449279376542, 97.35766851234727, 0.0, 0.05980341071622755, 0.0, 97.31731707317073, 0.0, 97.68317073170732, 5.01, 4.938137018479565, 9.768998999999999, 9.789279181608197, 0.7549220001000044, 0.6745499728944795, 0.5618840499147872, 0.0, 97.31731707317073, 0.0, 5.010000000000003, 0.0, 97.68317073170732, 97.68317073170732, 97.68317073170732, 5.010000000000002, 0.0, 5.009999999999995, 5.123753897040229, 9.768998999999996, 97.90950175975853, 97.90950175975853, 14.289572150099985, 5.727100407894995, 0.0, 97.68317073170732, 5.01, 97.79924387804878, 9.768999000000013, 97.57938834146586, 0.7549220001000014, 97.3375692367783, 97.3375692367783, 97.3375692367783, 0.0, 5.010000000000031, 9.768999000000022, 9.658299586930868, 9.605611540675781, 97.57938834146586, 0.7549220001000141, 0.5911027801486124, 0.5926492468066525, 5.727100407894986, 5.675452798224004, 10.450172677459449, 14.93661902631875, 14.818925123779565, 14.993040240894068, 19.198294413100182, 97.83234643963338, 11.126204024968898, 2.2477118070632827, 7.1451014455294395, 6.94633028479158, 0.0, 0.0, 97.31731707317073, 0.0, 0.10585642467379225, 5.009999999999994, 9.768998999999985, 14.289572150099996, 5.7271004078949925, 5.778478096865756, 5.6963943887337525, 10.450172677459427, 97.92528339088582, 14.936619026318738, 97.71801920163531, 6.438787267047974, 0.0, 0.01680863693812254, 97.31731707317073, 0.0, 0.0, 0.0, 5.009999999999999, 0.0, 97.31731707317073, 0.0, 5.00999999999999, 0.0, 0.0, 0.06403987518375408, 97.68340103491877, 5.0194424316693596, 4.831148703600182, 0.0, 97.68317073170732, 97.68317073170732, 5.009999999999984, 4.87700875362538, 4.816385111811499, 97.45171948780488, 97.45171948780488, 97.45171948780488, 0.0, 0.0, 5.009999999999998, 9.768998999999987, 9.874561437580835, 97.90950175975853, 14.289572150100001, 18.583664585379985, 98.11372251194274, 22.662622989652444, 22.714885973271702, 22.52141302325367, 22.730031716959694, 22.692498919554225, 22.577477915679612, 26.53722557787086, 26.62564975784752, 26.54678792453692, 30.21771057641954, 98.38326349454978, 98.38326349454978, 33.713803276540894, 33.59305030262048, 98.22175151765529, 98.22175151765529, 98.22175151765529, 98.22175151765529, 27.091812223867347, 98.31084176662075, 30.744512431451597, 23.82588922335359, 27.64221217326358, 27.551743263637228, 31.267337343383065, 34.71084374247958, 98.48736171880441, 37.98183047098135, 98.33624915451298, 98.33624915451298, 98.33624915451298, 31.786215335032402, 31.74763488911233, 31.78182457655323, 31.638282970501088, 35.20372594674728, 98.26172141875188, 28.730578168827336, 28.565845271983893, 98.34880917567243, 32.30117620256909, 35.69288727482037, 98.51011399078907, 38.914673622351884, 98.58475727985055, 41.97504847387204, 42.015877716907724, 98.65566094013003, 44.88209854533105, 44.94037126161229, 98.72301232702951, 47.64350540820996, 47.64350540820996, 50.26656578725864, 98.84776124003211, 98.84776124003211, 52.75821084131699, 98.90548840190651, 55.125024478167006, 55.05697628633056, 50.64201442353588, 98.67588174791334, 45.71115166444712, 48.43102296605832, 98.8052348467185, 98.8052348467185, 51.01462871545881, 50.98236428468459, 46.12099012413315, 48.820328518914074, 98.81425439171015, 98.81425439171015, 51.38443006011647, 46.52773462312212, 46.406720427080785, 98.56550866614566, 98.56550866614566, 41.185855311972006, 44.13244396084221, 98.70564411020499, 98.70564411020499, 46.93140851840402, 49.59014495163197, 44.55420043230001, 44.465841374493216, 98.71541548757662, 98.71541548757662, 47.332034990641766, 47.31893727053179, 98.77977317164904, 98.77977317164904, 98.77977317164904, 49.97070003761061, 44.97277297136792, 39.47555299120758, 39.50217349842903, 33.429160735029214, 33.49440337307293, 98.45766731176109, 98.45766731176109, 98.45766731176109, 36.764359782204245, 30.447119324446447, 98.38857850356808, 98.38857850356808, 33.931718646291685, 33.85293968636074, 27.33149733905622, 27.35816978501356, 27.274181384208646, 30.97218932236951, 24.076311035674223, 16.491534508138077, 20.675308629280362, 24.649475666953418, 97.97858434844103, 17.121958286082062, 21.274148175949335, 21.3199694062662, 13.409435578726661, 13.48682964445323, 17.74762285623248, 98.09435285246671, 21.868466951135225, 21.770966006910005, 21.804739492797953, 21.85866203546099, 25.782856756883355, 25.721913046618365, 25.733509969312625, 25.790579486376675, 25.796873981290357, 29.501135633363507, 29.478642734539246, 33.03312873813199, 33.05126984484, 33.10913465096647, 33.09947379788335, 98.44849192654516, 98.44849192654516, 98.44849192654516, 36.38816898835157, 36.4278693661558, 36.45919401304325, 36.384780557805556, 36.4581965529691, 39.57512172203517, 39.5485892677479, 39.62353193649883, 42.602408123761215, 42.53774246074548, 36.86838869532497, 36.91047042469161, 36.79600435381498, 40.03128242168917, 40.09598296759006, 43.03571517236255, 42.98483496865542, 42.96360231836289, 43.01442390432381, 98.68023477663968, 45.88962584222719, 98.74635501433004, 48.60055558753161, 98.62111588026161, 43.46575109072602, 43.50402866762826, 43.432578054456535, 43.33950439987346, 43.39111337858055, 43.50956074592824, 43.45062532628758, 43.352058460465194, 43.41276233136566, 43.4500941431253, 43.43952230262699, 43.50209179243894, 46.29811696108063, 46.192408265784465, 98.55934875232907, 98.55934875232907, 40.93329884549259, 40.97040008553717, 40.809037072332195, 40.84350289218911, 35.0325354001573, 28.542285686633008, 32.12231717373271, 32.10499537071686, 32.10170790697232, 32.15949682322449, 35.52298908332869, 35.43556477654344, 29.081735692753224, 32.634740734546284, 25.904951333927478, 29.617113272097704, 29.661903255890497, 29.548276400272833, 29.662792133334932, 33.14329589716562, 33.1643574678359, 36.49281677271762, 36.39750708469678, 36.43522704524212, 36.55443147437867, 98.52864699152207, 98.52864699152207, 39.67452665240447, 42.696832867119, 42.593163936791534, 36.97224647054419, 98.5397545590822, 98.5397545590822, 40.12993692236993, 40.0984117602858, 40.036323628432115, 34.148917620914695, 27.570394491244066, 27.414060336733186, 20.334676900919348, 12.376111123321177, 12.387416375632878, 16.7660679560428, 20.93608795144505, 20.809718059676648, 21.010752739372016, 20.956211490594136, 98.16822414500189, 98.16822414500189, 24.89718994507766, 97.9852297370876, 97.9852297370876, 17.39441922059093, 17.27292642398139, 98.08616972725949, 21.532958817639315, 21.62360365761439, 21.5485800176535, 13.694101403521481, 5.072142133733277, 97.45338656421691, 0.0, 5.010000000000011, 0.0, 5.010000000000019, 97.45171948780488, 0.0, 0.0, 0.1556314502448504, 5.195219974532037, 5.045968558437034, 9.94493945380799, 9.810814722528741, 0.9484389052434036, 5.910922116090713, 10.624784918074583, 10.699925331589691, 10.460687103828665, 97.60234636418025, 1.6962009313902189, 0.0, 5.010000000000004, 4.869133586511379, 5.0222708007246775, 97.45171948780488, 0.0, 5.010000000000021, 4.879006900486614, 9.768998999999983, 97.57938834146586, 0.7549220001000108, 0.5773953861890666, 0.0, 5.009999999999996, 9.768999, 0.7549220000999793, 0.677453548361242, 0.60515733816311, 97.3375692367783, 97.3375692367783, 97.3375692367783, 0.0, 5.010000000000002, 0.0, 97.68317073170732, 97.68317073170732, 5.009999999999997, 5.046656202310048, 4.972966717103117, 0.0, 0.08739750436006344, 97.68317073170732, 5.009999999999999, 0.0, 5.010000000000014, 9.768999000000006, 14.289572150100005, 5.727100407894978, 5.636619907976453, 5.77041334964459, 5.5915120685048985, 10.450172677459461, 10.248568796677649, 10.232616259540956, 97.92528339088581, 97.92528339088581, 14.936619026318734, 97.71801920163531, 6.438787267047982, 0.0, 97.68317073170732, 5.009999999999999, 97.79924387804877, 9.768999000000012, 97.57938834146586, 0.7549220000999942, 0.7683125004847348, 97.70066098555841, 5.727100407894973, 5.814154101909228, 5.817996995196633, 5.759516915321828, 5.514087373647958, 0.0, 0.0, 0.0, 0.10524120499577758, 0.005311663331485876, 0.09915449351280785]) - 1, 1]}
        });
        
            </script>
        </body>
        </html>
        