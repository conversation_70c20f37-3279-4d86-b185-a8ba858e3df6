"""
Simple test script to verify real data fetching works.
"""
import asyncio
import aiohttp
import sys
from datetime import datetime, timezone

# Fix for Windows event loop issue
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

async def test_binance_api():
    """Test direct Binance API call."""
    url = "https://api.binance.com/api/v3/klines"
    params = {
        'symbol': 'BTCUSDT',
        'interval': '1h',
        'limit': 5
    }

    # Use basic connector without DNS resolver
    connector = aiohttp.TCPConnector(use_dns_cache=False)

    async with aiohttp.ClientSession(connector=connector) as session:
        try:
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Successfully fetched {len(data)} candles")

                    # Print first candle
                    if data:
                        candle = data[0]
                        timestamp = datetime.fromtimestamp(candle[0] / 1000, tz=timezone.utc)
                        print(f"First candle: {timestamp} - O:{candle[1]} H:{candle[2]} L:{candle[3]} C:{candle[4]} V:{candle[5]}")

                    return True
                else:
                    print(f"❌ API request failed with status: {response.status}")
                    return False

        except Exception as e:
            print(f"❌ Error: {e}")
            return False

if __name__ == "__main__":
    print("Testing real Binance API connection...")
    success = asyncio.run(test_binance_api())
    if success:
        print("✅ Real data integration test passed!")
    else:
        print("❌ Real data integration test failed!")
