"""
Test Profit Calculation
Verify that profits are $30 and losses are $15 with proper commission
"""
import sys
from pathlib import Path
from datetime import datetime, timezone

# Add src to path
sys.path.append(str(Path(__file__).parent))

from src.trading.environment import GridTradingEnv, Action


def test_profit_calculation():
    """Test that profit calculations are correct."""
    print("🧪 TESTING PROFIT CALCULATIONS")
    print("=" * 50)
    print("Expected: $30 profit (10% of $300), $15 loss (5% of $300)")
    print("Commission: 0.1% of trade size for entry + exit")
    print("=" * 50)
    
    # Create environment with $300 initial balance
    env = GridTradingEnv(
        initial_balance=300.0,
        risk_per_trade=0.05,  # 5% risk
        grid_spacing=0.0025,  # 0.25%
        take_profit_multiplier=2.0,  # 2:1 ratio
        fee_rate=0.001  # 0.1%
    )
    
    # Test price
    test_price = 50000.0
    test_time = datetime.now(timezone.utc)
    
    # Reset environment
    env.reset(test_price, test_time)
    print(f"Initial balance: ${env.balance:.2f}")
    
    # Test LONG position
    print(f"\n📈 TESTING LONG POSITION")
    print(f"Entry price: ${test_price:.2f}")
    
    # Open long position
    env._open_position('long', test_price, test_time)
    
    if env.positions:
        position = env.positions[0]
        print(f"Position size: {position.size:.6f}")
        print(f"Position value: ${position.size * position.entry_price:.2f}")
        print(f"Stop loss: ${position.stop_loss:.2f}")
        print(f"Take profit: ${position.take_profit:.2f}")
        
        # Calculate expected profit/loss
        stop_loss_pct = (test_price - position.stop_loss) / test_price
        take_profit_pct = (position.take_profit - test_price) / test_price
        
        print(f"Stop loss %: {stop_loss_pct*100:.3f}%")
        print(f"Take profit %: {take_profit_pct*100:.3f}%")
        
        # Test take profit scenario
        print(f"\n✅ TAKE PROFIT SCENARIO:")
        position.update(position.take_profit)
        gross_profit = position.pnl
        trade_size = position.size * position.entry_price
        exit_fee = trade_size * env.fee_rate
        net_profit = gross_profit - exit_fee
        
        print(f"Gross profit: ${gross_profit:.2f}")
        print(f"Exit commission: ${exit_fee:.2f}")
        print(f"Net profit: ${net_profit:.2f}")
        print(f"Expected: ~$30.00")
        
        # Test stop loss scenario
        print(f"\n❌ STOP LOSS SCENARIO:")
        position.update(position.stop_loss)
        gross_loss = position.pnl
        net_loss = gross_loss - exit_fee
        
        print(f"Gross loss: ${gross_loss:.2f}")
        print(f"Exit commission: ${exit_fee:.2f}")
        print(f"Net loss: ${net_loss:.2f}")
        print(f"Expected: ~-$15.00")
        
        # Calculate total commission (entry + exit)
        entry_fee = trade_size * env.fee_rate
        total_commission = entry_fee + exit_fee
        print(f"\nTotal commission (entry + exit): ${total_commission:.2f}")
        
    else:
        print("❌ No position created")
    
    print(f"\n🎯 CALCULATION VERIFICATION:")
    print(f"Initial balance: $300.00")
    print(f"Risk per trade: 5% = $15.00")
    print(f"Profit target: 2:1 ratio = $30.00")
    print(f"Grid spacing: 0.25%")
    print(f"Stop loss: 0.125% move")
    print(f"Take profit: 0.25% move")
    print(f"Commission: 0.1% per trade side")


def test_multiple_scenarios():
    """Test multiple price scenarios."""
    print(f"\n🔄 TESTING MULTIPLE SCENARIOS")
    print("-" * 50)
    
    test_prices = [40000, 50000, 60000, 70000]
    
    for price in test_prices:
        print(f"\nPrice: ${price:,.2f}")
        
        env = GridTradingEnv(initial_balance=300.0, risk_per_trade=0.05, grid_spacing=0.0025, take_profit_multiplier=2.0, fee_rate=0.001)
        env.reset(price, datetime.now(timezone.utc))
        env._open_position('long', price, datetime.now(timezone.utc))
        
        if env.positions:
            pos = env.positions[0]
            trade_size = pos.size * pos.entry_price
            
            # Take profit
            pos.update(pos.take_profit)
            gross_profit = pos.pnl
            exit_fee = trade_size * env.fee_rate
            net_profit = gross_profit - exit_fee
            
            # Stop loss
            pos.update(pos.stop_loss)
            gross_loss = pos.pnl
            net_loss = gross_loss - exit_fee
            
            print(f"  Trade size: ${trade_size:.2f}")
            print(f"  Net profit: ${net_profit:.2f} (target: $30)")
            print(f"  Net loss: ${net_loss:.2f} (target: -$15)")
            print(f"  Commission: ${(trade_size * env.fee_rate * 2):.2f}")


if __name__ == "__main__":
    try:
        test_profit_calculation()
        test_multiple_scenarios()
        print(f"\n🎉 PROFIT CALCULATION TEST COMPLETED!")
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
