"""
Enhanced HTML Report Generator
Comprehensive trade-by-trade analysis with equity curves, drawdown charts, and full metrics
"""
from datetime import datetime
from typing import Dict, List
import json


class EnhancedHTMLReportGenerator:
    """Generate comprehensive HTML reports with detailed trade analysis."""

    def __init__(self):
        self.report_template = self._load_template()

    def generate_enhanced_report(self, analysis: Dict, best_cycles: List[Dict], timestamp: str) -> str:
        """Generate enhanced HTML report with full trade-by-trade analysis."""

        if 'error' in analysis:
            return f"<html><body><h1>Error in analysis: {analysis['error']}</h1></body></html>"

        # Extract data for charts
        composite_scores = analysis['raw_data']['composite_scores']
        returns = analysis['raw_data']['returns']

        # Get detailed data from best cycles
        best_cycle_data = self._extract_best_cycle_details(best_cycles)

        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Enhanced 1000-Cycle Evaluation Report</title>
            <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
            <style>
                {self._get_enhanced_css()}
            </style>
        </head>
        <body>
            <div class="container">
                {self._generate_header(analysis, timestamp)}
                {self._generate_executive_summary(analysis)}
                {self._generate_performance_charts(analysis)}
                {self._generate_composite_metrics_section(analysis)}
                {self._generate_best_cycles_section(best_cycles)}
                {self._generate_trade_by_trade_section(best_cycle_data)}
                {self._generate_equity_drawdown_section(best_cycle_data)}
                {self._generate_commission_analysis(best_cycle_data)}
                {self._generate_live_trading_readiness_section(analysis)}
                {self._generate_insights_section(analysis)}
            </div>

            <script>
                {self._generate_enhanced_charts_js(analysis, best_cycle_data)}
            </script>
        </body>
        </html>
        """

        return html_content

    def _extract_best_cycle_details(self, best_cycles: List[Dict]) -> Dict:
        """Extract detailed data from best performing cycles."""
        if not best_cycles:
            return {}

        best_cycle = best_cycles[0]  # Top performing cycle
        testing_data = best_cycle.get('testing', {})

        return {
            'cycle_number': best_cycle.get('cycle', 0),
            'composite_score': testing_data.get('composite_score', 0),
            'final_balance': testing_data.get('final_balance', 300),
            'total_return': testing_data.get('total_return', 0),
            'equity_curve': testing_data.get('equity_curve', [300]),
            'drawdown_curve': testing_data.get('drawdown_curve', [0]),
            'detailed_trades': testing_data.get('detailed_trades', []),
            'trade_actions': testing_data.get('trade_actions', []),
            'commission_paid': testing_data.get('commission_paid', 0),
            'composite_metrics': testing_data.get('composite_metrics', {})
        }

    def _generate_header(self, analysis: Dict, timestamp: str) -> str:
        """Generate enhanced header section."""
        # Determine cycle count and title
        total_cycles = analysis['summary'].get('total_cycles', analysis['summary'].get('valid_cycles', 1000))
        cycle_text = f"{total_cycles:,}-Cycle" if total_cycles >= 10000 else f"{total_cycles}-Cycle"

        # Live trading readiness badge
        readiness = analysis.get('live_trading_readiness', {})
        recommendation = readiness.get('recommendation', 'Unknown')

        if 'READY FOR LIVE TRADING' in recommendation:
            readiness_badge = '<div class="ready-badge">🚀 READY FOR LIVE TRADING</div>'
            badge_class = 'ready-badge'
        elif 'PROCEED WITH CAUTION' in recommendation:
            readiness_badge = '<div class="caution-badge">⚠️ PROCEED WITH CAUTION</div>'
            badge_class = 'caution-badge'
        elif 'NOT READY' in recommendation:
            readiness_badge = '<div class="not-ready-badge">❌ NOT READY FOR LIVE TRADING</div>'
            badge_class = 'not-ready-badge'
        else:
            readiness_badge = ''
            badge_class = ''

        return f"""
        <div class="header">
            <h1>🎯 Enhanced {cycle_text} Comprehensive Evaluation Report</h1>
            <p>60-Day Training + 30-Day Out-of-Sample Testing | Simple 5% Risk Model</p>
            <p>Optimized for Composite Metrics | Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            {readiness_badge}
            <div class="header-stats">
                <div class="stat-item">
                    <span class="stat-value">{analysis['summary']['valid_cycles']:,}</span>
                    <span class="stat-label">Valid Cycles</span>
                </div>
                <div class="stat-item">
                    <span class="stat-value">{analysis['composite_scores']['mean']:.3f}</span>
                    <span class="stat-label">Avg Composite</span>
                </div>
                <div class="stat-item">
                    <span class="stat-value">{analysis['returns']['mean']:+.1f}%</span>
                    <span class="stat-label">Avg Return</span>
                </div>
                <div class="stat-item">
                    <span class="stat-value">{analysis['returns']['positive_cycles']:,}</span>
                    <span class="stat-label">Profitable</span>
                </div>
                {f'<div class="stat-item"><span class="stat-value">{readiness.get("stability_score", 0):.0f}/100</span><span class="stat-label">Stability Score</span></div>' if readiness else ''}
            </div>
        </div>
        """

    def _generate_executive_summary(self, analysis: Dict) -> str:
        """Generate executive summary section."""
        return f"""
        <div class="section">
            <h2>📊 Executive Summary</h2>
            <div class="summary-grid">
                <div class="summary-card">
                    <h3>Performance Overview</h3>
                    <ul>
                        <li><strong>Success Rate:</strong> {analysis['summary']['success_rate']:.1f}%</li>
                        <li><strong>Profitable Cycles:</strong> {analysis['returns']['positive_cycles']}/{analysis['summary']['valid_cycles']} ({analysis['returns']['positive_cycles']/analysis['summary']['valid_cycles']*100:.1f}%)</li>
                        <li><strong>Best Composite Score:</strong> {analysis['composite_scores']['max']:.3f}</li>
                        <li><strong>Worst Composite Score:</strong> {analysis['composite_scores']['min']:.3f}</li>
                    </ul>
                </div>
                <div class="summary-card">
                    <h3>Return Statistics</h3>
                    <ul>
                        <li><strong>Mean Return:</strong> {analysis['returns']['mean']:+.2f}%</li>
                        <li><strong>Median Return:</strong> {analysis['returns']['median']:+.2f}%</li>
                        <li><strong>Best Return:</strong> {analysis['returns']['max']:+.2f}%</li>
                        <li><strong>Worst Return:</strong> {analysis['returns']['min']:+.2f}%</li>
                    </ul>
                </div>
                <div class="summary-card">
                    <h3>Trading Activity</h3>
                    <ul>
                        <li><strong>Avg Trades/Cycle:</strong> {analysis['trading_activity']['mean_trades']:.1f}</li>
                        <li><strong>Min Trades:</strong> {analysis['trading_activity']['min_trades']}</li>
                        <li><strong>Max Trades:</strong> {analysis['trading_activity']['max_trades']}</li>
                        <li><strong>Std Deviation:</strong> {analysis['trading_activity']['std_trades']:.1f}</li>
                    </ul>
                </div>
            </div>
        </div>
        """

    def _generate_trade_by_trade_section(self, best_cycle_data: Dict) -> str:
        """Generate detailed trade-by-trade analysis section."""
        if not best_cycle_data or not best_cycle_data.get('detailed_trades'):
            return '<div class="section"><h2>📋 Trade-by-Trade Analysis</h2><p>No detailed trade data available.</p></div>'

        trades_html = ""
        for trade in best_cycle_data['detailed_trades'][:20]:  # Show first 20 trades
            profit_class = "profit" if trade['pnl_net'] > 0 else "loss"
            trades_html += f"""
            <tr class="{profit_class}">
                <td>{trade['trade_id']}</td>
                <td>{trade['direction']}</td>
                <td>{trade['entry_time'].strftime('%Y-%m-%d %H:%M') if hasattr(trade['entry_time'], 'strftime') else str(trade['entry_time'])}</td>
                <td>{trade['exit_time'].strftime('%Y-%m-%d %H:%M') if hasattr(trade['exit_time'], 'strftime') else str(trade['exit_time'])}</td>
                <td>${trade['entry_price']:.2f}</td>
                <td>${trade['exit_price']:.2f}</td>
                <td>{trade['size']:.6f}</td>
                <td>${trade['pnl_gross']:+.2f}</td>
                <td>${trade['commission']:.2f}</td>
                <td>${trade['pnl_net']:+.2f}</td>
                <td>{trade['return_pct']:+.2f}%</td>
                <td>{trade['duration_hours']:.1f}h</td>
                <td>{trade['exit_reason']}</td>
            </tr>
            """

        return f"""
        <div class="section">
            <h2>📋 Trade-by-Trade Analysis (Best Cycle #{best_cycle_data.get('cycle_number', 'N/A')})</h2>
            <div class="trade-summary">
                <p><strong>Total Trades:</strong> {len(best_cycle_data['detailed_trades'])}</p>
                <p><strong>Total Commission Paid:</strong> ${best_cycle_data.get('commission_paid', 0):.2f}</p>
                <p><strong>Final Balance:</strong> ${best_cycle_data.get('final_balance', 300):.2f}</p>
                <p><strong>Total Return:</strong> {best_cycle_data.get('total_return', 0):+.2f}%</p>
            </div>
            <div class="table-container">
                <table class="trades-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Direction</th>
                            <th>Entry Time</th>
                            <th>Exit Time</th>
                            <th>Entry Price</th>
                            <th>Exit Price</th>
                            <th>Size</th>
                            <th>Gross P&L</th>
                            <th>Commission</th>
                            <th>Net P&L</th>
                            <th>Return %</th>
                            <th>Duration</th>
                            <th>Exit Reason</th>
                        </tr>
                    </thead>
                    <tbody>
                        {trades_html}
                    </tbody>
                </table>
            </div>
            {f'<p class="note">Showing first 20 of {len(best_cycle_data["detailed_trades"])} trades</p>' if len(best_cycle_data['detailed_trades']) > 20 else ''}
        </div>
        """

    def _generate_equity_drawdown_section(self, best_cycle_data: Dict) -> str:
        """Generate equity curve and drawdown charts section."""
        return f"""
        <div class="section">
            <h2>📈 Equity Curve & Drawdown Analysis (Best Cycle)</h2>
            <div class="chart-grid">
                <div class="chart-container" id="equity-curve"></div>
                <div class="chart-container" id="drawdown-chart"></div>
            </div>
            <div class="equity-stats">
                <div class="stat-card">
                    <h4>Equity Statistics</h4>
                    <p><strong>Starting Balance:</strong> $300.00</p>
                    <p><strong>Final Balance:</strong> ${best_cycle_data.get('final_balance', 300):.2f}</p>
                    <p><strong>Peak Balance:</strong> ${max(best_cycle_data.get('equity_curve', [300])):.2f}</p>
                    <p><strong>Total Return:</strong> {best_cycle_data.get('total_return', 0):+.2f}%</p>
                </div>
                <div class="stat-card">
                    <h4>Drawdown Statistics</h4>
                    <p><strong>Max Drawdown:</strong> {max(best_cycle_data.get('drawdown_curve', [0]))*100:.2f}%</p>
                    <p><strong>Avg Drawdown:</strong> {sum(best_cycle_data.get('drawdown_curve', [0]))/len(best_cycle_data.get('drawdown_curve', [1]))*100:.2f}%</p>
                    <p><strong>Recovery Factor:</strong> {abs(best_cycle_data.get('total_return', 0))/(max(best_cycle_data.get('drawdown_curve', [0.01]))*100):.2f}</p>
                </div>
            </div>
        </div>
        """

    def _generate_commission_analysis(self, best_cycle_data: Dict) -> str:
        """Generate commission analysis section."""
        total_commission = best_cycle_data.get('commission_paid', 0)
        final_balance = best_cycle_data.get('final_balance', 300)
        commission_pct = (total_commission / 300) * 100 if total_commission > 0 else 0

        return f"""
        <div class="section">
            <h2>💰 Commission Analysis (0.1% of Trade Size)</h2>
            <div class="commission-grid">
                <div class="commission-card">
                    <h3>Commission Details</h3>
                    <p><strong>Total Commission Paid:</strong> ${total_commission:.2f}</p>
                    <p><strong>Commission as % of Initial Capital:</strong> {commission_pct:.2f}%</p>
                    <p><strong>Commission Rate:</strong> 0.1% per trade (entry + exit = 0.2% total)</p>
                    <p><strong>Number of Trades:</strong> {len(best_cycle_data.get('detailed_trades', []))}</p>
                </div>
                <div class="commission-card">
                    <h3>Impact Analysis</h3>
                    <p><strong>Gross Return:</strong> {((final_balance + total_commission) / 300 - 1) * 100:+.2f}%</p>
                    <p><strong>Net Return (after commission):</strong> {best_cycle_data.get('total_return', 0):+.2f}%</p>
                    <p><strong>Commission Impact:</strong> -{(total_commission / 300) * 100:.2f}%</p>
                    <p><strong>Avg Commission per Trade:</strong> ${total_commission / max(len(best_cycle_data.get('detailed_trades', [])), 1):.2f}</p>
                </div>
            </div>
        </div>
        """

    def _get_enhanced_css(self) -> str:
        """Get enhanced CSS styles."""
        return """
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: #f5f7fa; }
        .container { max-width: 1600px; margin: 0 auto; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 40px; border-radius: 15px; text-align: center; margin-bottom: 30px; }
        .header h1 { margin: 0; font-size: 2.5em; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .header p { margin: 10px 0 0 0; opacity: 0.9; font-size: 1.2em; }
        .header-stats { display: flex; justify-content: center; gap: 30px; margin-top: 20px; }
        .stat-item { text-align: center; }
        .stat-value { display: block; font-size: 2em; font-weight: bold; }
        .stat-label { display: block; opacity: 0.8; }
        .section { background: white; margin: 20px 0; padding: 25px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
        .section h2 { color: #2c3e50; margin-top: 0; border-bottom: 3px solid #3498db; padding-bottom: 10px; }
        .summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .summary-card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #3498db; }
        .summary-card h3 { margin-top: 0; color: #2c3e50; }
        .summary-card ul { margin: 0; padding-left: 20px; }
        .summary-card li { margin: 8px 0; }
        .chart-container { margin: 25px 0; height: 400px; }
        .chart-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .trades-table { width: 100%; border-collapse: collapse; margin: 20px 0; font-size: 0.9em; }
        .trades-table th { background: #3498db; color: white; padding: 12px 8px; text-align: center; }
        .trades-table td { padding: 8px; border-bottom: 1px solid #eee; text-align: center; }
        .trades-table .profit { background: #d4edda; }
        .trades-table .loss { background: #f8d7da; }
        .table-container { overflow-x: auto; }
        .trade-summary { background: #e8f4fd; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
        .trade-summary p { margin: 5px 0; }
        .equity-stats { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 20px; }
        .stat-card { background: #f8f9fa; padding: 20px; border-radius: 8px; }
        .stat-card h4 { margin-top: 0; color: #2c3e50; }
        .stat-card p { margin: 8px 0; }
        .commission-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .commission-card { background: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid #ffc107; }
        .commission-card h3 { margin-top: 0; color: #856404; }
        .commission-card p { margin: 8px 0; }
        .note { font-style: italic; color: #666; margin-top: 10px; }
        .ready-badge { background: #28a745; color: white; padding: 10px 20px; border-radius: 25px; font-weight: bold; display: inline-block; margin: 15px 0; }
        .caution-badge { background: #ffc107; color: #212529; padding: 10px 20px; border-radius: 25px; font-weight: bold; display: inline-block; margin: 15px 0; }
        .not-ready-badge { background: #dc3545; color: white; padding: 10px 20px; border-radius: 25px; font-weight: bold; display: inline-block; margin: 15px 0; }
        .readiness-overview { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px; }
        .readiness-card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745; }
        .readiness-score { text-align: center; margin: 15px 0; }
        .score-value { display: block; font-size: 3em; font-weight: bold; color: #28a745; }
        .score-label { display: block; color: #666; margin-top: 5px; }
        .criteria-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; }
        .criteria-item { background: #f8f9fa; padding: 15px; border-radius: 8px; }
        .criteria-item h4 { margin-top: 0; color: #2c3e50; }
        .criteria-status { font-weight: bold; padding: 8px; border-radius: 5px; text-align: center; }
        .criteria-status.met { background: #d4edda; color: #155724; }
        .criteria-status.not-met { background: #f8d7da; color: #721c24; }
        """

    def _generate_performance_charts(self, analysis: Dict) -> str:
        """Generate performance distribution charts."""
        return f"""
        <div class="section">
            <h2>📈 Performance Distribution</h2>
            <div class="chart-grid">
                <div class="chart-container" id="composite-distribution"></div>
                <div class="chart-container" id="returns-distribution"></div>
            </div>
            <div class="chart-container" id="performance-categories"></div>
        </div>
        """

    def _generate_composite_metrics_section(self, analysis: Dict) -> str:
        """Generate composite metrics breakdown."""
        metrics_html = ""
        for metric_name, metric_data in analysis.get('composite_metrics_analysis', {}).items():
            metrics_html += f"""
            <div class="metric-breakdown">
                <h4>{metric_name.replace('_', ' ').title()}</h4>
                <p><strong>Mean:</strong> {metric_data['mean']:.4f}</p>
                <p><strong>Std:</strong> {metric_data['std']:.4f}</p>
                <p><strong>Range:</strong> {metric_data['min']:.4f} - {metric_data['max']:.4f}</p>
                <p><strong>Median:</strong> {metric_data['median']:.4f}</p>
            </div>
            """

        return f"""
        <div class="section">
            <h2>📋 Composite Metrics Breakdown</h2>
            <div class="composite-breakdown">
                {metrics_html}
            </div>
        </div>
        """

    def _generate_best_cycles_section(self, best_cycles: List[Dict]) -> str:
        """Generate best performing cycles section."""
        cycles_html = ""
        for i, cycle in enumerate(best_cycles[:10], 1):  # Top 10
            testing = cycle.get('testing', {})
            cycles_html += f"""
            <tr>
                <td>{i}</td>
                <td>{cycle.get('cycle', 'N/A')}</td>
                <td>{testing.get('composite_score', 0):.4f}</td>
                <td>{testing.get('total_return', 0):+.2f}%</td>
                <td>${testing.get('final_balance', 300):.2f}</td>
                <td>{testing.get('total_trades', 0)}</td>
                <td>{testing.get('winning_trades', 0)}</td>
                <td>{testing.get('losing_trades', 0)}</td>
                <td>{testing.get('max_drawdown', 0)*100:.2f}%</td>
                <td>${testing.get('commission_paid', 0):.2f}</td>
            </tr>
            """

        return f"""
        <div class="section">
            <h2>🏆 Best Performing Cycles</h2>
            <div class="table-container">
                <table class="trades-table">
                    <thead>
                        <tr>
                            <th>Rank</th>
                            <th>Cycle</th>
                            <th>Composite Score</th>
                            <th>Return</th>
                            <th>Final Balance</th>
                            <th>Total Trades</th>
                            <th>Winning</th>
                            <th>Losing</th>
                            <th>Max Drawdown</th>
                            <th>Commission</th>
                        </tr>
                    </thead>
                    <tbody>
                        {cycles_html}
                    </tbody>
                </table>
            </div>
        </div>
        """

    def _generate_live_trading_readiness_section(self, analysis: Dict) -> str:
        """Generate live trading readiness assessment section."""
        readiness = analysis.get('live_trading_readiness', {})

        if not readiness:
            return ""

        detailed_metrics = readiness.get('detailed_metrics', {})

        return f"""
        <div class="section">
            <h2>🚀 Live Trading Readiness Assessment</h2>
            <div class="readiness-overview">
                <div class="readiness-card">
                    <h3>Overall Assessment</h3>
                    <div class="readiness-score">
                        <span class="score-value">{readiness.get('stability_score', 0):.0f}/100</span>
                        <span class="score-label">Stability Score</span>
                    </div>
                    <p><strong>Recommendation:</strong> {readiness.get('recommendation', 'Unknown')}</p>
                    <p><strong>Consistency Rating:</strong> {readiness.get('consistency_rating', 'Unknown')}</p>
                    <p><strong>Risk Assessment:</strong> {readiness.get('risk_assessment', 'Unknown')}</p>
                </div>

                <div class="readiness-card">
                    <h3>Detailed Metrics</h3>
                    <p><strong>Composite Score Mean:</strong> {detailed_metrics.get('composite_score_mean', 0):.3f}</p>
                    <p><strong>Composite Score Std:</strong> {detailed_metrics.get('composite_score_std', 0):.3f}</p>
                    <p><strong>Return Mean:</strong> {detailed_metrics.get('return_mean', 0):+.2f}%</p>
                    <p><strong>Return Std:</strong> {detailed_metrics.get('return_std', 0):.2f}%</p>
                    <p><strong>Positive Return Ratio:</strong> {detailed_metrics.get('positive_return_ratio', 0):.1%}</p>
                    <p><strong>Total Cycles Analyzed:</strong> {detailed_metrics.get('total_cycles_analyzed', 0):,}</p>
                </div>
            </div>

            <div class="readiness-criteria">
                <h3>Live Trading Criteria Analysis</h3>
                <div class="criteria-grid">
                    <div class="criteria-item">
                        <h4>Stability (Score ≥ 70)</h4>
                        <div class="criteria-status {'met' if readiness.get('stability_score', 0) >= 70 else 'not-met'}">
                            {readiness.get('stability_score', 0):.0f}/100 - {'✅ MET' if readiness.get('stability_score', 0) >= 70 else '❌ NOT MET'}
                        </div>
                    </div>
                    <div class="criteria-item">
                        <h4>Consistency (Good/Excellent)</h4>
                        <div class="criteria-status {'met' if readiness.get('consistency_rating') in ['Good', 'Excellent'] else 'not-met'}">
                            {readiness.get('consistency_rating', 'Unknown')} - {'✅ MET' if readiness.get('consistency_rating') in ['Good', 'Excellent'] else '❌ NOT MET'}
                        </div>
                    </div>
                    <div class="criteria-item">
                        <h4>Risk Level (Low/Moderate)</h4>
                        <div class="criteria-status {'met' if readiness.get('risk_assessment') in ['Low', 'Moderate'] else 'not-met'}">
                            {readiness.get('risk_assessment', 'Unknown')} - {'✅ MET' if readiness.get('risk_assessment') in ['Low', 'Moderate'] else '❌ NOT MET'}
                        </div>
                    </div>
                    <div class="criteria-item">
                        <h4>Positive Return Rate (≥ 50%)</h4>
                        <div class="criteria-status {'met' if detailed_metrics.get('positive_return_ratio', 0) >= 0.5 else 'not-met'}">
                            {detailed_metrics.get('positive_return_ratio', 0):.1%} - {'✅ MET' if detailed_metrics.get('positive_return_ratio', 0) >= 0.5 else '❌ NOT MET'}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        """

    def _generate_insights_section(self, analysis: Dict) -> str:
        """Generate insights and recommendations section."""
        return f"""
        <div class="section">
            <h2>🎯 Key Insights & Recommendations</h2>
            <div style="background: #e8f4fd; padding: 20px; border-radius: 10px; border-left: 5px solid #3498db;">
                <h3>Performance Analysis:</h3>
                <ul>
                    <li><strong>Success Rate:</strong> {analysis['summary']['success_rate']:.1f}% of cycles completed successfully</li>
                    <li><strong>Profitability:</strong> {analysis['returns']['positive_cycles']}/{analysis['summary']['valid_cycles']} cycles were profitable ({analysis['returns']['positive_cycles']/analysis['summary']['valid_cycles']*100:.1f}%)</li>
                    <li><strong>Consistency:</strong> Composite score std deviation of {analysis['composite_scores']['std']:.3f}</li>
                    <li><strong>Best Performance:</strong> Top cycle achieved {analysis['composite_scores']['max']:.3f} composite score</li>
                    <li><strong>Commission Impact:</strong> Average commission reduces returns by approximately 0.1-0.3% per cycle</li>
                </ul>

                <h3>Trading Pattern Analysis:</h3>
                <ul>
                    <li><strong>Activity Level:</strong> Average {analysis['trading_activity']['mean_trades']:.1f} trades per 30-day test period</li>
                    <li><strong>Trade Frequency:</strong> Approximately {analysis['trading_activity']['mean_trades']/30:.1f} trades per day</li>
                    <li><strong>Consistency:</strong> Trade count std deviation of {analysis['trading_activity']['std_trades']:.1f}</li>
                </ul>

                <h3>Composite Metrics Performance:</h3>
                <ul>
                    <li><strong>Excellent Cycles (0.8+):</strong> {analysis['performance_categories']['excellent']['percentage']:.1f}% - These represent the model's peak potential</li>
                    <li><strong>Good Cycles (0.6-0.8):</strong> {analysis['performance_categories']['good']['percentage']:.1f}% - Solid performance with room for improvement</li>
                    <li><strong>Acceptable Cycles (0.4-0.6):</strong> {analysis['performance_categories']['acceptable']['percentage']:.1f}% - Moderate performance, needs optimization</li>
                    <li><strong>Poor Cycles (<0.4):</strong> {analysis['performance_categories']['poor']['percentage']:.1f}% - Requires significant improvement</li>
                </ul>

                <h3>Recommendations:</h3>
                <ul>
                    <li><strong>Model Optimization:</strong> Focus on improving cycles with composite scores below 0.4</li>
                    <li><strong>Pattern Analysis:</strong> Study top-performing cycles for common characteristics</li>
                    <li><strong>Risk Management:</strong> Implement dynamic position sizing based on market volatility</li>
                    <li><strong>Commission Optimization:</strong> Consider trade frequency vs commission cost trade-offs</li>
                    <li><strong>Ensemble Methods:</strong> Combine multiple best-performing models for improved consistency</li>
                    <li><strong>Market Conditions:</strong> Analyze performance correlation with different market regimes</li>
                </ul>
            </div>
        </div>
        """

    def _generate_enhanced_charts_js(self, analysis: Dict, best_cycle_data: Dict) -> str:
        """Generate JavaScript for enhanced charts."""
        composite_scores = analysis['raw_data']['composite_scores']
        returns = analysis['raw_data']['returns']
        equity_curve = best_cycle_data.get('equity_curve', [300])
        drawdown_curve = [d * 100 for d in best_cycle_data.get('drawdown_curve', [0])]  # Convert to percentage

        return f"""
        // Composite Score Distribution
        var compositeData = [{{
            x: {composite_scores},
            type: 'histogram',
            nbinsx: 30,
            name: 'Composite Scores',
            marker: {{color: '#3498db'}}
        }}];

        Plotly.newPlot('composite-distribution', compositeData, {{
            title: 'Distribution of Composite Scores (1000 Cycles)',
            xaxis: {{title: 'Composite Score'}},
            yaxis: {{title: 'Frequency'}}
        }});

        // Returns Distribution
        var returnsData = [{{
            x: {returns},
            type: 'histogram',
            nbinsx: 30,
            name: 'Returns',
            marker: {{color: '#e74c3c'}}
        }}];

        Plotly.newPlot('returns-distribution', returnsData, {{
            title: 'Distribution of Returns (1000 Cycles)',
            xaxis: {{title: 'Return (%)'}},
            yaxis: {{title: 'Frequency'}}
        }});

        // Performance Categories Pie Chart
        var categoryData = [{{
            values: [{analysis['performance_categories']['excellent']['count']},
                    {analysis['performance_categories']['good']['count']},
                    {analysis['performance_categories']['acceptable']['count']},
                    {analysis['performance_categories']['poor']['count']}],
            labels: ['Excellent (0.8+)', 'Good (0.6-0.8)', 'Acceptable (0.4-0.6)', 'Poor (<0.4)'],
            type: 'pie',
            marker: {{colors: ['#27ae60', '#f39c12', '#e67e22', '#e74c3c']}}
        }}];

        Plotly.newPlot('performance-categories', categoryData, {{
            title: 'Performance Category Distribution'
        }});

        // Equity Curve
        var equityData = [{{
            y: {equity_curve},
            type: 'scatter',
            mode: 'lines',
            name: 'Equity Curve',
            line: {{color: '#2ecc71', width: 2}}
        }}];

        Plotly.newPlot('equity-curve', equityData, {{
            title: 'Equity Curve (Best Cycle)',
            xaxis: {{title: 'Time (Hours)'}},
            yaxis: {{title: 'Balance ($)'}}
        }});

        // Drawdown Chart
        var drawdownData = [{{
            y: {drawdown_curve},
            type: 'scatter',
            mode: 'lines',
            fill: 'tozeroy',
            name: 'Drawdown',
            line: {{color: '#e74c3c'}},
            fillcolor: 'rgba(231, 76, 60, 0.3)'
        }}];

        Plotly.newPlot('drawdown-chart', drawdownData, {{
            title: 'Drawdown Chart (Best Cycle)',
            xaxis: {{title: 'Time (Hours)'}},
            yaxis: {{title: 'Drawdown (%)', range: [Math.min(...{drawdown_curve}) - 1, 1]}}
        }});
        """

    def _load_template(self) -> str:
        """Load HTML template (placeholder for future template file)."""
        return ""
