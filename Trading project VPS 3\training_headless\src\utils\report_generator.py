"""
Report Generator for Grid Trading System
Generates comprehensive HTML reports with equity curve, metrics, and trade details
"""
import os
import pandas as pd
import numpy as np
from datetime import datetime
from pathlib import Path
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px

class TradingReport:
    def __init__(self, initial_capital=300, output_dir='reports'):
        """Initialize the report generator
        
        Args:
            initial_capital: Starting capital in USD
            output_dir: Directory to save HTML reports
        """
        self.initial_capital = initial_capital
        self.current_balance = initial_capital
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Trade tracking
        self.active_trade = None
        self.trade_history = []
        
        # Initialize data structures
        self.trades = []
        self.equity_curve = [initial_capital]
        self.timestamps = [datetime.now()]
        
    def add_trade(self, timestamp, action, entry_price, exit_price=None, 
                 quantity=None, pnl=0, current_balance=None):
        """Add a trade to the report
        
        Args:
            timestamp: Trade timestamp
            action: 'BUY', 'SELL', 'TP_HIT', or 'SL_HIT'
            entry_price: Entry price
            exit_price: Exit price (for TP/SL)
            quantity: Position size (calculated if None)
            pnl: Profit/Loss in USD (calculated if None)
            current_balance: Current account balance (calculated if None)
        """
        # Handle new trade entry
        if action in ['BUY', 'SELL']:
            if self.active_trade is not None:
                raise ValueError("Cannot open new trade while another trade is active")
                
            # Calculate position size (5% of balance)
            if quantity is None:
                risk_amount = self.current_balance * 0.05
                quantity = risk_amount / (entry_price * 0.00125)  # 0.125% SL = 2:1 R:R with 0.25% TP
                
            # Store active trade
            self.active_trade = {
                'entry_time': timestamp,
                'action': action,
                'entry_price': entry_price,
                'quantity': quantity,
                'tp_price': entry_price * (1.0025 if action == 'BUY' else 0.9975),  # 0.25% TP
                'sl_price': entry_price * (0.99875 if action == 'BUY' else 1.00125)  # 0.125% SL
            }
            
            trade = {
                'timestamp': timestamp,
                'action': action,
                'entry_price': entry_price,
                'exit_price': None,
                'quantity': quantity,
                'pnl': 0,
                'balance': self.current_balance,
                'exit_type': None
            }
            self.trades.append(trade)
            
        # Handle trade exit (TP/SL)
        elif action in ['TP_HIT', 'SL_HIT'] and self.active_trade is not None:
            if exit_price is None:
                exit_price = self.active_trade['tp_price'] if action == 'TP_HIT' else self.active_trade['sl_price']
                
            # Calculate P&L
            if self.active_trade['action'] == 'BUY':
                pnl = (exit_price - self.active_trade['entry_price']) * self.active_trade['quantity']
            else:  # SELL
                pnl = (self.active_trade['entry_price'] - exit_price) * self.active_trade['quantity']
                
            # Update balance
            self.current_balance += pnl
            if current_balance is None:
                current_balance = self.current_balance
                
            # Update the last trade with exit info
            if self.trades:
                self.trades[-1].update({
                    'exit_price': exit_price,
                    'pnl': pnl,
                    'exit_time': timestamp,
                    'balance': current_balance,
                    'exit_type': action
                })
                
            # Clear active trade
            self.active_trade = None
            
            # Update equity curve
            self.equity_curve.append(current_balance)
            self.timestamps.append(timestamp)
        else:
            raise ValueError("No active trade to exit")
    
    def check_price_update(self, timestamp, current_price):
        """Check if current price hits TP or SL for active trade
        
        Returns:
            dict or None: Trade result if TP/SL hit, None otherwise
        """
        if self.active_trade is None:
            return None
            
        action = self.active_trade['action']
        
        # Check for TP hit
        if (action == 'BUY' and current_price >= self.active_trade['tp_price']) or \
           (action == 'SELL' and current_price <= self.active_trade['tp_price']):
            self.add_trade(timestamp, 'TP_HIT', self.active_trade['entry_price'], 
                         self.active_trade['tp_price'], self.active_trade['quantity'])
            return {'action': 'TP_HIT', 'price': self.active_trade['tp_price']}
            
        # Check for SL hit
        elif (action == 'BUY' and current_price <= self.active_trade['sl_price']) or \
             (action == 'SELL' and current_price >= self.active_trade['sl_price']):
            self.add_trade(timestamp, 'SL_HIT', self.active_trade['entry_price'], 
                         self.active_trade['sl_price'], self.active_trade['quantity'])
            return {'action': 'SL_HIT', 'price': self.active_trade['sl_price']}
            
        return None
        
    def calculate_metrics(self):
        """Calculate trading performance metrics"""
        if not self.trades:
            return {}
            
        # Filter out trades that haven't been closed yet
        closed_trades = [t for t in self.trades if 'exit_type' in t and t['exit_type'] is not None]
        if not closed_trades:
            return {}
            
        df = pd.DataFrame(closed_trades)
        
        # Basic metrics
        total_trades = len(df)
        winning_trades = len(df[df['pnl'] > 0])
        losing_trades = len(df[df['pnl'] < 0])
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # PnL metrics
        total_pnl = df['pnl'].sum()
        avg_win = df[df['pnl'] > 0]['pnl'].mean() if winning_trades > 0 else 0
        avg_loss = abs(df[df['pnl'] < 0]['pnl'].mean()) if losing_trades > 0 else 0
        profit_factor = (winning_trades * avg_win) / (losing_trades * avg_loss) if losing_trades > 0 else float('inf')
        
        # Risk metrics
        max_win = df['pnl'].max()
        max_loss = df['pnl'].min()
        
        # Drawdown calculation
        peak = self.initial_capital
        max_drawdown = 0
        for balance in self.equity_curve:
            if balance > peak:
                peak = balance
            drawdown = (peak - balance) / peak * 100
            if drawdown > max_drawdown:
                max_drawdown = drawdown
        
        # Combine all metrics
        metrics = {
            'initial_capital': self.initial_capital,
            'final_balance': self.equity_curve[-1],
            'total_return': (self.equity_curve[-1] - self.initial_capital) / self.initial_capital * 100,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate * 100,
            'profit_factor': profit_factor,
            'max_win': max_win,
            'max_loss': max_loss,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'max_drawdown': max_drawdown,
            'avg_trade': df['pnl'].mean(),
            'sharpe_ratio': self._calculate_sharpe_ratio(),
            'sortino_ratio': self._calculate_sortino_ratio(),
            'calmar_ratio': self._calculate_calmar_ratio(),
            'risk_of_ruin': self._calculate_risk_of_ruin()
        }
        
        return metrics
    
    def _calculate_sharpe_ratio(self, risk_free_rate=0.0):
        """Calculate Sharpe ratio"""
        returns = np.diff(self.equity_curve) / self.equity_curve[:-1]
        if len(returns) < 2:
            return 0
        excess_returns = returns - risk_free_rate / 252  # Assuming daily returns
        return np.sqrt(252) * np.mean(excess_returns) / np.std(excess_returns)
    
    def _calculate_sortino_ratio(self, risk_free_rate=0.0):
        """Calculate Sortino ratio"""
        returns = np.diff(self.equity_curve) / self.equity_curve[:-1]
        if len(returns) < 2:
            return 0
        excess_returns = returns - risk_free_rate / 252
        downside_returns = returns[returns < 0]
        if len(downside_returns) == 0:
            return float('inf')
        downside_std = np.std(downside_returns)
        return np.sqrt(252) * np.mean(excess_returns) / downside_std if downside_std != 0 else float('inf')
    
    def _calculate_calmar_ratio(self):
        """Calculate Calmar ratio"""
        total_return = (self.equity_curve[-1] - self.initial_capital) / self.initial_capital
        max_dd = self.calculate_metrics()['max_drawdown'] / 100  # Convert from % to decimal
        return total_return / max_dd if max_dd != 0 else float('inf')
    
    def _calculate_risk_of_ruin(self, simulations=1000):
        """Calculate risk of ruin using Monte Carlo simulation"""
        if not self.trades:
            return 100.0
            
        df = pd.DataFrame(self.trades)
        win_rate = len(df[df['pnl'] > 0]) / len(df)
        avg_win = df[df['pnl'] > 0]['pnl'].mean() if len(df[df['pnl'] > 0]) > 0 else 0
        avg_loss = abs(df[df['pnl'] < 0]['pnl'].mean()) if len(df[df['pnl'] < 0]) > 0 else 0
        
        if avg_win == 0 or avg_loss == 0:
            return 50.0  # Neutral if we can't determine
            
        ruin_count = 0
        
        for _ in range(simulations):
            balance = self.initial_capital
            position = 0
            
            while balance > 0 and balance < self.initial_capital * 2:  # Stop if doubled or ruined
                if np.random.random() < win_rate:
                    balance += avg_win
                else:
                    balance -= avg_loss
                    
                if balance <= 0:
                    ruin_count += 1
                    break
                    
        return (ruin_count / simulations) * 100
    
    def generate_html_report(self, filename=None):
        """Generate comprehensive HTML report"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'grid_trading_report_{timestamp}.html'
        
        filepath = self.output_dir / filename
        
        # Calculate metrics
        metrics = self.calculate_metrics()
        
        # Create figures
        equity_fig = self._create_equity_curve_figure()
        metrics_fig = self._create_metrics_figure(metrics)
        trades_fig = self._create_trades_heatmap()
        
        # Create HTML content
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Grid Trading Performance Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ text-align: center; margin-bottom: 30px; }}
                .section {{ margin-bottom: 40px; }}
                .metrics-grid {{
                    display: grid;
                    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
                    gap: 15px;
                    margin-bottom: 20px;
                }}
                .metric-card {{
                    background: #f9f9f9;
                    border-radius: 5px;
                    padding: 15px;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                }}
                .metric-value {{
                    font-size: 24px;
                    font-weight: bold;
                    color: #2c3e50;
                }}
                .positive {{ color: #27ae60; }}
                .negative {{ color: #e74c3c; }}
                table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                th, td {{ padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }}
                th {{ background-color: #f2f2f2; }}
                tr:hover {{ background-color: #f5f5f5; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Grid Trading Performance Report</h1>
                <p>Strategy: 0.25% Grid | Initial Capital: ${self.initial_capital:,.2f} | Risk per Trade: 5%</p>
                <p>Generated on {date}</p>
            </div>
            
            <div class="section">
                <h2>Equity Curve</h2>
                {equity_chart}
            </div>
            
            <div class="section">
                <h2>Performance Metrics</h2>
                {metrics_grid}
                
                <h3>Key Ratios</h3>
                {metrics_chart}
            </div>
            
            <div class="section">
                <h2>Trade Analysis</h2>
                <h3>Trade Heatmap</h3>
                {trades_heatmap}
                
                <h3>Trade History</h3>
                {trades_table}
            </div>
        </body>
        </html>
        """.format(
            date=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            equity_chart=equity_fig.to_html(full_html=False, include_plotlyjs='cdn'),
            metrics_grid=self._create_metrics_grid(metrics),
            metrics_chart=metrics_fig.to_html(full_html=False, include_plotlyjs='cdn'),
            trades_heatmap=trades_fig.to_html(full_html=False, include_plotlyjs='cdn'),
            trades_table=self._create_trades_table()
        )
        
        # Save to file
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(html_content)
            
        return filepath
    
    def _create_equity_curve_figure(self):
        """Create equity curve figure"""
        fig = go.Figure()
        
        # Add equity curve
        fig.add_trace(go.Scatter(
            x=self.timestamps,
            y=self.equity_curve,
            mode='lines',
            name='Equity',
            line=dict(color='#3498db', width=2)
        ))
        
        # Add drawdown area
        peak = self.initial_capital
        drawdowns = []
        for balance in self.equity_curve:
            if balance > peak:
                peak = balance
            drawdown = (peak - balance) / peak * 100
            drawdowns.append(drawdown)
            
        fig.add_trace(go.Scatter(
            x=self.timestamps,
            y=drawdowns,
            fill='tozeroy',
            name='Drawdown %',
            yaxis='y2',
            line=dict(color='rgba(231, 76, 60, 0.3)', width=1)
        ))
        
        # Layout
        fig.update_layout(
            title='Equity Curve & Drawdown',
            xaxis_title='Date',
            yaxis_title='Equity ($)',
            yaxis2=dict(
                title='Drawdown %',
                titlefont=dict(color='#e74c3c'),
                tickfont=dict(color='#e74c3c'),
                anchor='x',
                overlaying='y',
                side='right',
                showgrid=False
            ),
            legend=dict(orientation='h', y=1.1, yanchor='bottom', x=0.5, xanchor='center'),
            hovermode='x unified',
            plot_bgcolor='rgba(0,0,0,0.02)',
            height=500
        )
        
        return fig
    
    def _create_metrics_grid(self, metrics):
        """Create HTML grid of metrics"""
        if not metrics:
            return "<p>No metrics available</p>"
            
        metric_cards = []
        
        # Define metrics to show in the grid
        grid_metrics = [
            ('Initial Capital', '${:,.2f}'.format(metrics['initial_capital']), None),
            ('Final Balance', '${:,.2f}'.format(metrics['final_balance']), 
             'positive' if metrics['final_balance'] >= metrics['initial_capital'] else 'negative'),
            ('Total Return', '{:+.2f}%'.format(metrics['total_return']), 
             'positive' if metrics['total_return'] >= 0 else 'negative'),
            ('Total Trades', '{:,}'.format(metrics['total_trades']), None),
            ('Win Rate', '{:.1f}%'.format(metrics['win_rate']), 
             'positive' if metrics['win_rate'] >= 50 else 'negative'),
            ('Profit Factor', '{:.2f}'.format(metrics['profit_factor']) if metrics['profit_factor'] != float('inf') else '∞',
             'positive' if metrics['profit_factor'] > 1.5 else 'negative'),
            ('Max Drawdown', '{:.2f}%'.format(metrics['max_drawdown']), 
             'negative' if metrics['max_drawdown'] > 15 else 'positive'),
            ('Avg Win/Avg Loss', '{:.2f}'.format(metrics['avg_win'] / abs(metrics['avg_loss'])) if metrics['avg_loss'] != 0 else 'N/A',
             'positive' if metrics['avg_win'] > abs(metrics['avg_loss']) else 'negative'),
            ('Sharpe Ratio', '{:.2f}'.format(metrics['sharpe_ratio']), 
             'positive' if metrics['sharpe_ratio'] > 1 else 'negative'),
            ('Sortino Ratio', '{:.2f}'.format(metrics['sortino_ratio']) if metrics['sortino_ratio'] != float('inf') else '∞',
             'positive' if metrics['sortino_ratio'] > 1 else 'negative'),
            ('Calmar Ratio', '{:.2f}'.format(metrics['calmar_ratio']) if metrics['calmar_ratio'] != float('inf') else '∞',
             'positive' if metrics['calmar_ratio'] > 1 else 'negative'),
            ('Risk of Ruin', '{:.1f}%'.format(metrics['risk_of_ruin']), 
             'negative' if metrics['risk_of_ruin'] > 5 else 'positive')
        ]
        
        for name, value, css_class in grid_metrics:
            if css_class:
                card = f"""
                <div class="metric-card">
                    <div class="metric-name">{name}</div>
                    <div class="metric-value {css_class}">{value}</div>
                </div>
                """
            else:
                card = f"""
                <div class="metric-card">
                    <div class="metric-name">{name}</div>
                    <div class="metric-value">{value}</div>
                </div>
                """
            metric_cards.append(card)
            
        return f'<div class="metrics-grid">{"".join(metric_cards)}</div>'
    
    def _create_metrics_figure(self, metrics):
        """Create metrics radar chart"""
        if not metrics:
            return go.Figure()
            
        categories = ['Win Rate', 'Profit Factor', 'Sharpe Ratio', 'Sortino Ratio', 'Calmar Ratio']
        
        # Normalize metrics to 0-1 scale for radar chart
        normalized_metrics = [
            min(metrics['win_rate'] / 100, 1.0),  # Win Rate (0-100%)
            min(metrics['profit_factor'] / 5, 1.0) if metrics['profit_factor'] != float('inf') else 1.0,  # Profit Factor (0-5)
            min(metrics['sharpe_ratio'] / 3, 1.0),  # Sharpe Ratio (0-3)
            min(metrics['sortino_ratio'] / 3, 1.0) if metrics['sortino_ratio'] != float('inf') else 1.0,  # Sortino Ratio (0-3)
            min(metrics['calmar_ratio'] / 3, 1.0) if metrics['calmar_ratio'] != float('inf') else 1.0  # Calmar Ratio (0-3)
        ]
        
        fig = go.Figure()
        
        fig.add_trace(go.Scatterpolar(
            r=normalized_metrics,
            theta=categories,
            fill='toself',
            name='Performance',
            line=dict(color='#3498db')
        ))
        
        fig.update_layout(
            polar=dict(
                radialaxis=dict(
                    visible=True,
                    range=[0, 1],
                    showticklabels=False
                )
            ),
            showlegend=False,
            height=400
        )
        
        return fig
    
    def _create_trades_heatmap(self):
        """Create heatmap of trades by hour and day of week"""
        if not self.trades:
            return go.Figure()
            
        df = pd.DataFrame(self.trades)
        df['hour'] = df['timestamp'].dt.hour
        df['day_of_week'] = df['timestamp'].dt.day_name()
        
        # Define order of days
        days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        
        # Create pivot table for heatmap
        pivot = df.pivot_table(
            index='hour',
            columns='day_of_week',
            values='pnl',
            aggfunc='count',
            fill_value=0
        )
        
        # Ensure all days are present
        for day in days:
            if day not in pivot.columns:
                pivot[day] = 0
        
        # Reorder columns
        pivot = pivot[days]
        
        fig = px.imshow(
            pivot,
            labels=dict(x="Day of Week", y="Hour", color="Trades"),
            x=pivot.columns,
            y=pivot.index,
            color_continuous_scale='Viridis',
            aspect="auto"
        )
        
        fig.update_layout(
            title='Trades by Time of Day and Day of Week',
            xaxis_title='Day of Week',
            yaxis_title='Hour of Day',
            height=400
        )
        
        return fig
    
    def _create_trades_table(self):
        """Create HTML table of trades"""
        if not self.trades:
            return "<p>No trades recorded</p>"
            
        df = pd.DataFrame(self.trades)
        
        # Format columns
        df['entry_time'] = df['timestamp'].dt.strftime('%Y-%m-%d %H:%M:%S')
        df['exit_time'] = df.get('exit_time', '').apply(
            lambda x: x.strftime('%Y-%m-%d %H:%M:%S') if pd.notnull(x) else 'Active')
        df['entry_price'] = df['entry_price'].apply(lambda x: f"${x:,.2f}")
        df['exit_price'] = df['exit_price'].apply(lambda x: f"${x:,.2f}" if pd.notnull(x) else 'N/A')
        df['pnl'] = df['pnl'].apply(lambda x: f"<span class='{'positive' if x >= 0 else 'negative'}'>{x:+,.2f}</span>")
        df['balance'] = df['balance'].apply(lambda x: f"${x:,.2f}" if pd.notnull(x) else 'N/A')
        
        # Add trade duration
        if 'exit_time' in df.columns and 'entry_time' in df.columns:
            df['duration'] = (pd.to_datetime(df['exit_time']) - pd.to_datetime(df['entry_time'])).astype('timedelta64[m]').astype(str) + 'm'
            df['duration'] = df['duration'].replace('NaTm', 'Active')
        
        # Create HTML table
        columns = ['entry_time', 'action', 'entry_price', 'exit_time', 'exit_price', 
                 'exit_type', 'quantity', 'pnl', 'balance']
        headers = ['Entry Time', 'Action', 'Entry', 'Exit Time', 'Exit', 
                  'Exit Type', 'Qty', 'P&L', 'Balance']
        
        # Reorder columns to put exit_type next to exit_price
        if 'exit_type' in df.columns:
            exit_type_idx = columns.index('exit_type')
            columns.pop(exit_type_idx)
            columns.insert(columns.index('exit_price') + 1, 'exit_type')
            
        table = df.to_html(
            columns=columns,
            index=False,
            escape=False,
            classes='trades-table',
            header=headers
        )
        
        return table
