"""Evaluation script for the TCN-CNN-PPO trading model."""

import os
import json
import argparse
from pathlib import Path
from typing import Dict, Any, List, Tuple, Optional

import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt
from tqdm import tqdm

from src.models.tcn_cnn_ppo import TCN_CNN_PPO
from src.agent.ppo_agent import PPOAgent
from src.env.trading_env import CryptoTradingEnv
from src.data.data_loader import CryptoDataLoader
from src.data.transforms import Compose, AddIndicators, Normalize
from src.config import load_config


def load_model(checkpoint_path: str, config: Dict[str, Any], device: torch.device) -> TCN_CNN_PPO:
    """Load a trained model from checkpoint."""
    model = TCN_CNN_PPO(
        obs_dim=config["model"]["obs_dim"],
        action_dim=config["model"]["action_dim"],
        tcn_channels=config["model"]["tcn_channels"],
        cnn_filters=config["model"]["cnn_filters"],
        kernel_size=config["model"]["kernel_size"],
        dropout=0.0  # Disable dropout for evaluation
    ).to(device)
    
    checkpoint = torch.load(checkpoint_path, map_location=device)
    model.load_state_dict(checkpoint["model_state_dict"])
    model.eval()
    
    return model


def create_eval_environment(
    config: Dict[str, Any],
    symbol: str,
    start_date: str,
    end_date: str
) -> CryptoTradingEnv:
    """Create an evaluation environment."""
    # Initialize data loader
    data_loader = CryptoDataLoader(
        data_dir=config["data"]["data_dir"],
        symbols=[symbol],
        timeframe=config["data"]["timeframe"],
        indicators=config["data"].get("indicators", [])
    )
    
    # Load and prepare data
    data = data_loader.load_data(
        symbol=symbol,
        start_date=start_date,
        end_date=end_date
    )
    
    # Setup transforms
    transforms = Compose([
        AddIndicators(config["data"].get("indicators", [])),
        Normalize(method="standard")
    ])
    
    # Apply transforms
    scaled_data = transforms(data.values.astype(np.float32))
    
    # Create environment
    env = CryptoTradingEnv(
        data=scaled_data,
        window_size=config["env"]["window_size"],
        initial_balance=config["env"]["initial_balance"],
        commission=config["env"]["commission"],
        max_position=config["env"]["max_position"],
        use_pnl=config["env"]["use_pnl"],
        use_sharpe=config["env"]["use_sharpe"],
        use_drawdown=config["env"]["use_drawdown"],
        reward_scale=config["env"]["reward_scale"]
    )
    
    return env, data.index


def evaluate_model(
    model: TCN_CNN_PPO,
    env: CryptoTradingEnv,
    device: torch.device,
    timestamps: pd.DatetimeIndex
) -> Dict[str, Any]:
    """Evaluate the model on the given environment."""
    # Initialize agent
    agent = PPOAgent(
        model=model,
        optimizer=None,  # No optimizer needed for evaluation
        device=device,
        gamma=0.99,  # These values don't affect evaluation
        epsilon=0.0,  # No exploration
        entropy_coef=0.0,
        ppo_epochs=1,
        batch_size=1,
        clip_param=0.0
    )
    
    # Run evaluation
    obs = env.reset()
    done = False
    
    # Store results
    portfolio_values = [env.initial_balance]
    actions = []
    rewards = []
    positions = []
    prices = []
    
    pbar = tqdm(total=len(timestamps) - env.window_size, desc="Evaluating")
    
    while not done:
        # Get action from agent
        with torch.no_grad():
            action_probs, _ = model(torch.FloatTensor(obs).unsqueeze(0).to(device))
            action = torch.argmax(action_probs).item()
        
        # Take step in environment
        next_obs, reward, done, info = env.step(action)
        
        # Store results
        portfolio_values.append(info["portfolio_value"])
        actions.append(action)
        rewards.append(reward)
        positions.append(info["position"])
        prices.append(info["current_price"])
        
        # Update observation
        obs = next_obs
        pbar.update(1)
    
    pbar.close()
    
    # Calculate metrics
    returns = np.diff(portfolio_values) / portfolio_values[:-1]
    sharpe_ratio = np.mean(returns) / (np.std(returns) + 1e-9) * np.sqrt(252)
    
    # Calculate drawdown
    peak = np.maximum.accumulate(portfolio_values)
    drawdown = (peak - portfolio_values) / peak
    max_drawdown = np.max(drawdown)
    
    # Calculate trade statistics
    actions = np.array(actions)
    num_trades = np.sum(actions != 1)  # Count non-hold actions
    
    # Calculate win rate
    trade_returns = []
    in_trade = False
    entry_price = 0
    
    for i in range(len(actions)):
        if actions[i] == 0:  # Buy
            in_trade = True
            entry_price = prices[i]
        elif actions[i] == 2 and in_trade:  # Sell
            trade_return = (prices[i] - entry_price) / entry_price
            trade_returns.append(trade_return)
            in_trade = False
    
    win_rate = np.mean(np.array(trade_returns) > 0) if trade_returns else 0
    
    return {
        "portfolio_values": portfolio_values,
        "actions": actions,
        "rewards": rewards,
        "positions": positions,
        "prices": prices,
        "metrics": {
            "final_portfolio": portfolio_values[-1],
            "total_return": (portfolio_values[-1] / portfolio_values[0] - 1) * 100,
            "sharpe_ratio": sharpe_ratio,
            "max_drawdown": max_drawdown * 100,
            "num_trades": num_trades,
            "win_rate": win_rate * 100,
            "avg_trade_return": np.mean(trade_returns) * 100 if trade_returns else 0
        },
        "timestamps": timestamps[env.window_size:].tolist()
    }


def plot_results(results: Dict[str, Any], output_dir: Path) -> None:
    """Plot evaluation results."""
    # Create output directory
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Extract data
    timestamps = pd.to_datetime(results["timestamps"])
    portfolio_values = results["portfolio_values"]
    prices = results["prices"]
    positions = results["positions"]
    actions = results["actions"]
    
    # Create figure with subplots
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(15, 12), gridspec_kw={'height_ratios': [2, 1, 1]})
    
    # Plot portfolio value
    ax1.plot(timestamps, portfolio_values[1:], label="Portfolio Value", color="blue")
    ax1.set_title("Portfolio Value Over Time")
    ax1.set_ylabel("Value (USD)")
    ax1.grid(True)
    
    # Add secondary y-axis for price
    ax1b = ax1.twinx()
    ax1b.plot(timestamps, prices, label="Price", color="gray", alpha=0.3)
    ax1b.set_ylabel("Price (USD)")
    
    # Add buy/sell markers
    buy_times = [timestamps[i] for i, a in enumerate(actions) if a == 0]
    sell_times = [timestamps[i] for i, a in enumerate(actions) if a == 2]
    
    if buy_times:
        ax1.scatter(buy_times, [portfolio_values[i+1] for i, a in enumerate(actions) if a == 0], 
                   color="green", marker="^", label="Buy", alpha=0.7)
    if sell_times:
        ax1.scatter(sell_times, [portfolio_values[i+1] for i, a in enumerate(actions) if a == 2], 
                   color="red", marker="v", label="Sell", alpha=0.7)
    
    ax1.legend(loc="upper left")
    
    # Plot positions
    ax2.plot(timestamps, positions, label="Position", color="purple")
    ax2.set_title("Position Over Time")
    ax2.set_ylabel("Position")
    ax2.grid(True)
    
    # Plot drawdown
    peak = np.maximum.accumulate(portfolio_values)
    drawdown = (peak - portfolio_values) / peak
    ax3.fill_between(timestamps, drawdown[1:] * 100, 0, color="red", alpha=0.3)
    ax3.set_title("Drawdown")
    ax3.set_ylabel("Drawdown (%)")
    ax3.set_xlabel("Date")
    ax3.grid(True)
    
    # Add metrics to plot
    metrics = results["metrics"]
    metrics_text = (
        f"Total Return: {metrics['total_return']:.2f}%\n"
        f"Sharpe Ratio: {metrics['sharpe_ratio']:.2f}\n"
        f"Max Drawdown: {metrics['max_drawdown']:.2f}%\n"
        f"Trades: {metrics['num_trades']}\n"
        f"Win Rate: {metrics['win_rate']:.1f}%"
    )
    
    plt.figtext(0.15, 0.9, metrics_text, bbox={"facecolor": "white", "alpha": 0.8, "pad": 10})
    
    # Save figure
    plt.tight_layout()
    plt.savefig(output_dir / "evaluation.png", dpi=300, bbox_inches="tight")
    plt.close()


def save_metrics(metrics: Dict[str, Any], output_dir: Path) -> None:
    """Save evaluation metrics to file."""
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Save metrics as JSON
    with open(output_dir / "metrics.json", "w") as f:
        json.dump(metrics, f, indent=2)
    
    # Save metrics as CSV
    metrics_flat = {
        "final_portfolio": metrics["final_portfolio"],
        "total_return_pct": metrics["total_return"],
        "sharpe_ratio": metrics["sharpe_ratio"],
        "max_drawdown_pct": metrics["max_drawdown"],
        "num_trades": metrics["num_trades"],
        "win_rate_pct": metrics["win_rate"],
        "avg_trade_return_pct": metrics["avg_trade_return"]
    }
    
    pd.DataFrame([metrics_flat]).to_csv(output_dir / "metrics.csv", index=False)


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Evaluate TCN-CNN-PPO trading model")
    parser.add_argument("--config", type=str, required=True,
                        help="Path to config file")
    parser.add_argument("--checkpoint", type=str, required=True,
                        help="Path to model checkpoint")
    parser.add_argument("--symbol", type=str, default="BTC/USDT",
                        help="Trading pair symbol")
    parser.add_argument("--start-date", type=str, required=True,
                        help="Start date (YYYY-MM-DD)")
    parser.add_argument("--end-date", type=str, required=True,
                        help="End date (YYYY-MM-DD)")
    parser.add_argument("--output-dir", type=str, default="evaluation",
                        help="Output directory for results")
    parser.add_argument("--device", type=str, default=None,
                        help="Device to use (cpu, cuda, cuda:0, etc.)")
    return parser.parse_args()


def main():
    """Main evaluation function."""
    # Parse arguments
    args = parse_args()
    
    # Set device
    device = args.device or ("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")
    
    # Load config
    config = load_config(args.config)
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Load model
    print("Loading model...")
    model = load_model(args.checkpoint, config, device)
    
    # Create environment
    print("Creating environment...")
    env, timestamps = create_eval_vironment(
        config=config,
        symbol=args.symbol,
        start_date=args.start_date,
        end_date=args.end_date
    )
    
    # Evaluate model
    print(f"Evaluating from {args.start_date} to {args.end_date}...")
    results = evaluate_model(model, env, device, timestamps)
    
    # Save and plot results
    print("Saving results...")
    save_metrics(results["metrics"], output_dir)
    plot_results(results, output_dir)
    
    # Save raw results
    with open(output_dir / "raw_results.json", "w") as f:
        json.dump(results, f, indent=2)
    
    # Print summary
    print("\n=== Evaluation Complete ===")
    print(f"Results saved to: {output_dir.absolute()}")
    print("\n=== Metrics ===")
    for key, value in results["metrics"].items():
        print(f"{key}: {value:.4f}" if isinstance(value, float) else f"{key}: {value}")


if __name__ == "__main__":
    main()
