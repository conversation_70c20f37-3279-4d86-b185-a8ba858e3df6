"""
Demo: 5 Sample Trades with Real Market Data
Shows entry/exit, stop loss, take profit, 5% risk with compounding balance
"""
import asyncio
import sys
from datetime import datetime, timedelta, timezone

# Fix for Windows event loop issue
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

from src.data.binance_fetcher import BinanceDataFetcher
from src.trading.environment import GridTradingEnv, Action


async def demo_5_trades():
    """Demonstrate 5 sample trades with real market data."""
    print("🎯 TRADING LOGIC DEMONSTRATION")
    print("5 Sample Trades with Real BTC/USDT Data")
    print("=" * 60)
    print("📊 Strategy: Grid Trading with 5% Risk per Trade")
    print("📈 Risk-Reward: 2:1 (0.25% TP / 0.125% SL)")
    print("💰 Compounding: Balance updates after each trade")
    print("=" * 60)

    # Fetch real market data (last 50 hours for variety)
    async with BinanceDataFetcher() as fetcher:
        end_time = datetime.now(timezone.utc)
        start_time = end_time - timedelta(hours=50)

        response = await fetcher.fetch_historical_data(
            symbol="BTCUSDT",
            interval="1h",
            start_time=start_time,
            end_time=end_time
        )

        if not response.success:
            print(f"❌ Failed to fetch data: {response.error}")
            return

        market_data = response.data
        print(f"✅ Fetched {len(market_data)} real market candles")
        print(f"📅 Period: {market_data[0].timestamp} to {market_data[-1].timestamp}")
        print(f"💵 Price Range: ${min(c.low for c in market_data):.2f} - ${max(c.high for c in market_data):.2f}")

    # Initialize trading environment
    initial_balance = 10000.0
    env = GridTradingEnv(
        initial_balance=initial_balance,
        risk_per_trade=0.05,  # 5% risk per trade
        grid_spacing=0.0025,  # 0.25% grid spacing
        take_profit_multiplier=2.0,  # 2:1 risk-reward
        fee_rate=0.001  # 0.1% trading fee
    )

    # Reset with first candle
    first_candle = market_data[0]
    env.reset(first_candle.close, first_candle.timestamp)

    print(f"\n💼 Starting Balance: ${env.balance:,.2f}")
    print(f"🎯 Risk per Trade: {env.risk_per_trade*100}% = ${env.balance * env.risk_per_trade:,.2f}")
    print(f"📏 Grid Spacing: {env.grid_spacing*100}% (0.25%)")
    print(f"🎯 Take Profit: {env.grid_spacing * env.take_profit_multiplier * 100}% (0.5%)")
    print(f"🛑 Stop Loss: {env.grid_spacing / 2 * 100}% (0.125%)")

    # Execute 5 trades
    trades_executed = 0
    target_trades = 5

    print(f"\n{'='*60}")
    print("🔥 EXECUTING 5 SAMPLE TRADES")
    print(f"{'='*60}")

    for i, candle in enumerate(market_data[1:], 1):
        if trades_executed >= target_trades:
            break

        # Calculate price change from previous candle
        prev_candle = market_data[i-1]
        price_change = (candle.close - prev_candle.close) / prev_candle.close

        # Trading logic: Enter trades on price movements (more aggressive for demo)
        action = Action.HOLD
        signal_reason = ""

        if len(env.positions) == 0:  # Only enter if no open positions
            if price_change < -0.003:  # Price dropped more than 0.3%
                action = Action.BUY
                signal_reason = f"Price dip: {price_change:.2%}"
                trades_executed += 1
            elif price_change > 0.003:  # Price rose more than 0.3%
                action = Action.SELL
                signal_reason = f"Price spike: {price_change:.2%}"
                trades_executed += 1

        # Execute step
        prev_balance = env.balance
        prev_equity = env.equity
        state, reward, done, info = env.step(action, candle.close, candle.timestamp, candle)

        # Log trade entry
        if action != Action.HOLD and env.positions:
            position = env.positions[0]
            risk_amount = prev_balance * env.risk_per_trade

            print(f"\n📈 TRADE #{trades_executed}")
            print(f"⏰ Time: {candle.timestamp}")
            print(f"📊 Signal: {signal_reason}")
            print(f"🎯 Action: {'🟢 LONG' if action == Action.BUY else '🔴 SHORT'}")
            print(f"💰 Entry Price: ${position.entry_price:,.2f}")
            print(f"📏 Position Size: {position.size:.6f} BTC")
            print(f"💵 Position Value: ${position.entry_price * position.size:,.2f}")
            print(f"🎯 Take Profit: ${position.take_profit:,.2f} ({((position.take_profit/position.entry_price-1)*100):+.3f}%)")
            print(f"🛑 Stop Loss: ${position.stop_loss:,.2f} ({((position.stop_loss/position.entry_price-1)*100):+.3f}%)")
            print(f"⚖️ Risk Amount: ${risk_amount:,.2f} ({env.risk_per_trade*100}% of balance)")
            print(f"💼 Balance After Entry: ${env.balance:,.2f}")
            print(f"📊 Equity: ${env.equity:,.2f}")

        # Check for position closures
        if len(env.closed_positions) > getattr(env, '_last_closed_count', 0):
            closed_position = env.closed_positions[-1]

            # Determine exit reason
            exit_reason = ""
            if closed_position.current_price <= closed_position.stop_loss and closed_position.position_type == 'long':
                exit_reason = "🛑 STOP LOSS"
            elif closed_position.current_price >= closed_position.stop_loss and closed_position.position_type == 'short':
                exit_reason = "🛑 STOP LOSS"
            elif closed_position.current_price >= closed_position.take_profit and closed_position.position_type == 'long':
                exit_reason = "🎯 TAKE PROFIT"
            elif closed_position.current_price <= closed_position.take_profit and closed_position.position_type == 'short':
                exit_reason = "🎯 TAKE PROFIT"
            else:
                exit_reason = "⏰ TIME EXIT"

            pnl_emoji = "💰" if closed_position.pnl > 0 else "💸"

            print(f"\n🔚 TRADE EXIT")
            print(f"⏰ Exit Time: {candle.timestamp}")
            print(f"🚪 Exit Reason: {exit_reason}")
            print(f"💰 Exit Price: ${closed_position.current_price:,.2f}")
            print(f"{pnl_emoji} P&L: ${closed_position.pnl:,.2f} ({closed_position.pnl_pct:+.2%})")
            print(f"💼 New Balance: ${env.balance:,.2f}")
            print(f"📊 New Equity: ${env.equity:,.2f}")
            print(f"📈 Total Return: {((env.equity/initial_balance-1)*100):+.2f}%")

            env._last_closed_count = len(env.closed_positions)

        if not hasattr(env, '_last_closed_count'):
            env._last_closed_count = 0

    # Final summary
    print(f"\n{'='*60}")
    print("📊 FINAL TRADING SUMMARY")
    print(f"{'='*60}")

    metrics = env.get_metrics()

    print(f"💼 Starting Balance: ${initial_balance:,.2f}")
    print(f"💰 Final Balance: ${metrics['balance']:,.2f}")
    print(f"📊 Final Equity: ${metrics['equity']:,.2f}")
    print(f"📈 Total Return: {metrics['return_pct']:+.2f}%")
    print(f"📉 Max Drawdown: {metrics['max_drawdown']:.2f}%")
    print(f"🎯 Total Trades: {int(metrics['trades'])}")
    print(f"🏆 Win Rate: {metrics['win_rate']:.1f}%")
    print(f"📊 Sharpe Ratio: {metrics['sharpe_ratio']:.2f}")

    # Trade-by-trade breakdown
    if env.closed_positions:
        print(f"\n📋 TRADE-BY-TRADE BREAKDOWN")
        print(f"{'='*60}")
        total_pnl = 0

        for i, pos in enumerate(env.closed_positions, 1):
            total_pnl += pos.pnl
            pnl_emoji = "💰" if pos.pnl > 0 else "💸"
            print(f"Trade {i}: {pos.position_type.upper()} | "
                  f"Entry: ${pos.entry_price:,.2f} | "
                  f"Exit: ${pos.current_price:,.2f} | "
                  f"{pnl_emoji} P&L: ${pos.pnl:,.2f} ({pos.pnl_pct:+.2%})")

        print(f"\n💰 Total P&L: ${total_pnl:,.2f}")
        print(f"📊 Compounding Effect: Balance grew from ${initial_balance:,.2f} to ${metrics['balance']:,.2f}")

    print(f"\n✅ Demo completed successfully!")
    print(f"🎯 System demonstrates proper risk management with real market data")


if __name__ == "__main__":
    asyncio.run(demo_5_trades())
