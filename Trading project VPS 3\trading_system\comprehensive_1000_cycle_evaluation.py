"""
Comprehensive 1000-Cycle Evaluation System
60-day training + 30-day out-of-sample testing
Optimized for composite metrics with detailed HTML reporting
"""
import asyncio
import sys
import numpy as np
import pandas as pd
import torch
import json
from datetime import datetime, timedelta, timezone
from pathlib import Path
import logging
from typing import Dict, List, Tuple
import matplotlib.pyplot as plt
import seaborn as sns

# Fix for Windows event loop issue
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# Add src to path
sys.path.append(str(Path(__file__).parent))

from src.data.binance_fetcher import BinanceDataFetcher
from src.ml.integrated_training import IntegratedTrainingPipeline, CompositeMetricsCalculator
from enhanced_html_report_generator import EnhancedHTMLReportGenerator

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class Comprehensive1000CycleEvaluator:
    """1000-cycle evaluation with 60-day training + 30-day out-of-sample testing."""

    def __init__(self, initial_capital: float = 300.0):
        self.initial_capital = initial_capital
        self.training_days = 60
        self.test_days = 30
        self.total_cycles = 1000

        # Results storage
        self.evaluation_results = []
        self.best_models = []

        logger.info(f"Initialized 1000-cycle evaluator:")
        logger.info(f"  Training: {self.training_days} days")
        logger.info(f"  Testing: {self.test_days} days")
        logger.info(f"  Total cycles: {self.total_cycles}")
        logger.info(f"  Initial capital: ${self.initial_capital}")

    async def collect_evaluation_data(self) -> Dict[str, List]:
        """Collect comprehensive data for evaluation."""
        logger.info("Collecting comprehensive market data...")

        total_days = self.training_days + self.test_days + 30  # Extra buffer

        async with BinanceDataFetcher() as fetcher:
            end_time = datetime.now(timezone.utc)
            start_time = end_time - timedelta(days=total_days)

            response = await fetcher.fetch_historical_data(
                symbol="BTCUSDT",
                interval="1h",
                start_time=start_time,
                end_time=end_time
            )

            if not response.success:
                raise RuntimeError(f"Failed to fetch data: {response.error}")

            all_candles = response.data

            # Split data: 60 days training + 30 days testing
            train_hours = self.training_days * 24
            test_hours = self.test_days * 24

            training_data = all_candles[:train_hours]
            test_data = all_candles[train_hours:train_hours + test_hours]

            logger.info(f"Data splits:")
            logger.info(f"  Training: {len(training_data)} candles ({self.training_days} days)")
            logger.info(f"  Testing: {len(test_data)} candles ({self.test_days} days)")

            return {
                'training': training_data,
                'testing': test_data
            }

    async def run_single_cycle(self, cycle_num: int, data_splits: Dict) -> Dict:
        """Run a single evaluation cycle with detailed trade tracking."""
        try:
            # Initialize fresh pipeline for each cycle
            pipeline = IntegratedTrainingPipeline(initial_balance=self.initial_capital)

            # Training phase (reduced episodes for 1000 cycles)
            logger.info(f"Cycle {cycle_num}: Training phase...")
            training_episodes = 20  # Reduced for 1000 cycles

            # Override training data
            pipeline.training_data = data_splits['training']

            # Train model
            training_history = []
            best_composite_score = 0

            for episode in range(training_episodes):
                episode_data = await pipeline.simulate_episode(data_splits['training'], pipeline.model)

                if episode_data['composite_score'] > best_composite_score:
                    best_composite_score = episode_data['composite_score']

                # Quick training update
                if len(episode_data['states']) > 0:
                    await pipeline.quick_training_update(episode_data)

                training_history.append({
                    'episode': episode,
                    'composite_score': episode_data['composite_score'],
                    'final_balance': episode_data['final_balance'],
                    'total_return': episode_data['total_return']
                })

            # Testing phase (out-of-sample) with detailed tracking
            logger.info(f"Cycle {cycle_num}: Testing phase...")
            test_episode = await pipeline.simulate_episode_detailed(data_splits['testing'], pipeline.model)

            # Calculate drawdown curve
            drawdown_curve = self.calculate_drawdown_curve(test_episode['equity_curve'])

            # Calculate comprehensive results
            cycle_result = {
                'cycle': cycle_num,
                'training': {
                    'best_composite_score': best_composite_score,
                    'final_episode_score': training_history[-1]['composite_score'] if training_history else 0,
                    'episodes_trained': len(training_history),
                    'training_history': training_history
                },
                'testing': {
                    'composite_score': test_episode['composite_score'],
                    'composite_metrics': test_episode['composite_metrics'],
                    'final_balance': test_episode['final_balance'],
                    'total_return': test_episode['total_return'],
                    'total_trades': len(test_episode['trades']),
                    'equity_curve': test_episode['equity_curve'],
                    'drawdown_curve': drawdown_curve,
                    'detailed_trades': test_episode['detailed_trades'],
                    'trade_actions': test_episode['trade_actions'],
                    'commission_paid': test_episode['commission_paid'],
                    'max_drawdown': max(drawdown_curve) if drawdown_curve else 0,
                    'winning_trades': len([t for t in test_episode['trades'] if t.get('pnl', 0) > 0]),
                    'losing_trades': len([t for t in test_episode['trades'] if t.get('pnl', 0) < 0])
                }
            }

            # Log progress
            if cycle_num % 10 == 0:
                logger.info(f"Cycle {cycle_num}: Test Composite={test_episode['composite_score']:.4f}, "
                           f"Return={test_episode['total_return']:+.2f}%, "
                           f"Trades={len(test_episode['trades'])}, "
                           f"MaxDD={max(drawdown_curve) if drawdown_curve else 0:.1%}")

            return cycle_result

        except Exception as e:
            logger.error(f"Cycle {cycle_num} failed: {e}")
            return {
                'cycle': cycle_num,
                'error': str(e),
                'training': {'best_composite_score': 0, 'final_episode_score': 0, 'episodes_trained': 0},
                'testing': {'composite_score': 0, 'final_balance': self.initial_capital, 'total_return': 0, 'total_trades': 0}
            }

    def calculate_drawdown_curve(self, equity_curve: List[float]) -> List[float]:
        """Calculate drawdown curve from equity curve."""
        if not equity_curve or len(equity_curve) < 2:
            return []

        drawdowns = []
        peak = equity_curve[0]

        for equity in equity_curve:
            if equity > peak:
                peak = equity
            drawdown = (peak - equity) / peak
            drawdowns.append(drawdown)

        return drawdowns

    async def run_comprehensive_evaluation(self) -> List[Dict]:
        """Run the complete 1000-cycle evaluation."""
        logger.info("🚀 STARTING 1000-CYCLE COMPREHENSIVE EVALUATION")
        logger.info("=" * 80)
        logger.info("📊 Training: 60 days | Testing: 30 days | Optimized for composite metrics")
        logger.info("=" * 80)

        # Collect data once for all cycles
        data_splits = await self.collect_evaluation_data()

        # Run 1000 cycles
        results = []
        start_time = datetime.now()

        for cycle in range(self.total_cycles):
            cycle_result = await self.run_single_cycle(cycle, data_splits)
            results.append(cycle_result)

            # Progress reporting
            if cycle % 100 == 0 and cycle > 0:
                elapsed = datetime.now() - start_time
                avg_time_per_cycle = elapsed.total_seconds() / cycle
                estimated_remaining = timedelta(seconds=avg_time_per_cycle * (self.total_cycles - cycle))

                logger.info(f"Progress: {cycle}/{self.total_cycles} cycles completed")
                logger.info(f"Elapsed: {elapsed}, Estimated remaining: {estimated_remaining}")

                # Quick stats
                valid_results = [r for r in results if 'error' not in r]
                if valid_results:
                    avg_composite = np.mean([r['testing']['composite_score'] for r in valid_results])
                    avg_return = np.mean([r['testing']['total_return'] for r in valid_results])
                    logger.info(f"Current averages: Composite={avg_composite:.4f}, Return={avg_return:+.2f}%")

        logger.info("✅ 1000-cycle evaluation completed!")
        return results

    def analyze_results(self, results: List[Dict]) -> Dict:
        """Analyze the 1000-cycle results."""
        logger.info("📊 Analyzing 1000-cycle results...")

        # Filter valid results
        valid_results = [r for r in results if 'error' not in r]
        failed_cycles = len(results) - len(valid_results)

        if not valid_results:
            return {'error': 'No valid results to analyze'}

        # Extract metrics
        composite_scores = [r['testing']['composite_score'] for r in valid_results]
        returns = [r['testing']['total_return'] for r in valid_results]
        trades = [r['testing']['total_trades'] for r in valid_results]
        final_balances = [r['testing']['final_balance'] for r in valid_results]

        # Composite metrics breakdown
        all_metrics = {}
        for metric_name in ['win_rate', 'equity_growth', 'sortino_ratio', 'calmar_ratio',
                           'profit_factor', 'max_drawdown', 'risk_of_ruin', 'trade_frequency']:
            metric_values = []
            for r in valid_results:
                if 'composite_metrics' in r['testing']:
                    metric_values.append(r['testing']['composite_metrics'].get(metric_name, 0))
            all_metrics[metric_name] = metric_values

        # Performance categories
        excellent_cycles = [r for r in valid_results if r['testing']['composite_score'] >= 0.8]
        good_cycles = [r for r in valid_results if 0.6 <= r['testing']['composite_score'] < 0.8]
        acceptable_cycles = [r for r in valid_results if 0.4 <= r['testing']['composite_score'] < 0.6]
        poor_cycles = [r for r in valid_results if r['testing']['composite_score'] < 0.4]

        # Best performing cycles
        best_cycles = sorted(valid_results, key=lambda x: x['testing']['composite_score'], reverse=True)[:10]

        analysis = {
            'summary': {
                'total_cycles': len(results),
                'valid_cycles': len(valid_results),
                'failed_cycles': failed_cycles,
                'success_rate': len(valid_results) / len(results) * 100
            },
            'composite_scores': {
                'mean': np.mean(composite_scores),
                'std': np.std(composite_scores),
                'min': np.min(composite_scores),
                'max': np.max(composite_scores),
                'median': np.median(composite_scores),
                'percentiles': {
                    '25th': np.percentile(composite_scores, 25),
                    '75th': np.percentile(composite_scores, 75),
                    '90th': np.percentile(composite_scores, 90),
                    '95th': np.percentile(composite_scores, 95)
                }
            },
            'returns': {
                'mean': np.mean(returns),
                'std': np.std(returns),
                'min': np.min(returns),
                'max': np.max(returns),
                'median': np.median(returns),
                'positive_cycles': len([r for r in returns if r > 0]),
                'negative_cycles': len([r for r in returns if r < 0])
            },
            'trading_activity': {
                'mean_trades': np.mean(trades),
                'std_trades': np.std(trades),
                'min_trades': np.min(trades),
                'max_trades': np.max(trades)
            },
            'performance_categories': {
                'excellent': {'count': len(excellent_cycles), 'percentage': len(excellent_cycles)/len(valid_results)*100},
                'good': {'count': len(good_cycles), 'percentage': len(good_cycles)/len(valid_results)*100},
                'acceptable': {'count': len(acceptable_cycles), 'percentage': len(acceptable_cycles)/len(valid_results)*100},
                'poor': {'count': len(poor_cycles), 'percentage': len(poor_cycles)/len(valid_results)*100}
            },
            'composite_metrics_analysis': {},
            'best_cycles': best_cycles[:5],  # Top 5
            'raw_data': {
                'composite_scores': composite_scores,
                'returns': returns,
                'trades': trades,
                'final_balances': final_balances
            }
        }

        # Analyze each composite metric
        for metric_name, values in all_metrics.items():
            if values:
                analysis['composite_metrics_analysis'][metric_name] = {
                    'mean': np.mean(values),
                    'std': np.std(values),
                    'min': np.min(values),
                    'max': np.max(values),
                    'median': np.median(values)
                }

        return analysis

    def generate_comprehensive_html_report(self, analysis: Dict, timestamp: str) -> str:
        """Generate comprehensive HTML report with charts and detailed analysis."""
        if 'error' in analysis:
            return f"<html><body><h1>Error in analysis: {analysis['error']}</h1></body></html>"

        # Extract data for charts
        composite_scores = analysis['raw_data']['composite_scores']
        returns = analysis['raw_data']['returns']
        trades = analysis['raw_data']['trades']

        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>1000-Cycle Comprehensive Evaluation Report</title>
            <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
            <style>
                body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: #f5f7fa; }}
                .container {{ max-width: 1400px; margin: 0 auto; }}
                .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 40px; border-radius: 15px; text-align: center; margin-bottom: 30px; }}
                .header h1 {{ margin: 0; font-size: 2.5em; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }}
                .header p {{ margin: 10px 0 0 0; opacity: 0.9; font-size: 1.2em; }}
                .section {{ background: white; margin: 20px 0; padding: 25px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }}
                .section h2 {{ color: #2c3e50; margin-top: 0; border-bottom: 3px solid #3498db; padding-bottom: 10px; }}
                .metric-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }}
                .metric-card {{ background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%); color: white; padding: 20px; border-radius: 10px; text-align: center; }}
                .metric-value {{ font-size: 2em; font-weight: bold; margin-bottom: 5px; }}
                .metric-label {{ opacity: 0.9; }}
                .chart-container {{ margin: 25px 0; height: 400px; }}
                .performance-table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                .performance-table th {{ background: #3498db; color: white; padding: 12px; }}
                .performance-table td {{ padding: 10px; border-bottom: 1px solid #eee; text-align: center; }}
                .excellent {{ background: #d4edda; }}
                .good {{ background: #fff3cd; }}
                .acceptable {{ background: #f8d7da; }}
                .poor {{ background: #f5c6cb; }}
                .composite-breakdown {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; }}
                .metric-breakdown {{ background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #3498db; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🎯 1000-Cycle Comprehensive Evaluation Report</h1>
                    <p>60-Day Training + 30-Day Out-of-Sample Testing</p>
                    <p>Optimized for Composite Metrics | Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                </div>

                <div class="section">
                    <h2>📊 Executive Summary</h2>
                    <div class="metric-grid">
                        <div class="metric-card">
                            <div class="metric-value">{analysis['summary']['valid_cycles']}</div>
                            <div class="metric-label">Valid Cycles</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">{analysis['composite_scores']['mean']:.3f}</div>
                            <div class="metric-label">Avg Composite Score</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">{analysis['returns']['mean']:+.1f}%</div>
                            <div class="metric-label">Avg Return</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">{analysis['returns']['positive_cycles']}</div>
                            <div class="metric-label">Profitable Cycles</div>
                        </div>
                    </div>
                </div>

                <div class="section">
                    <h2>📈 Performance Distribution</h2>
                    <div class="chart-container" id="composite-distribution"></div>
                    <div class="chart-container" id="returns-distribution"></div>
                </div>

                <div class="section">
                    <h2>🏆 Performance Categories</h2>
                    <table class="performance-table">
                        <tr>
                            <th>Category</th>
                            <th>Composite Score Range</th>
                            <th>Count</th>
                            <th>Percentage</th>
                            <th>Status</th>
                        </tr>
                        <tr class="excellent">
                            <td>Excellent</td>
                            <td>0.8 - 1.0</td>
                            <td>{analysis['performance_categories']['excellent']['count']}</td>
                            <td>{analysis['performance_categories']['excellent']['percentage']:.1f}%</td>
                            <td>🏆 Outstanding</td>
                        </tr>
                        <tr class="good">
                            <td>Good</td>
                            <td>0.6 - 0.8</td>
                            <td>{analysis['performance_categories']['good']['count']}</td>
                            <td>{analysis['performance_categories']['good']['percentage']:.1f}%</td>
                            <td>✅ Strong</td>
                        </tr>
                        <tr class="acceptable">
                            <td>Acceptable</td>
                            <td>0.4 - 0.6</td>
                            <td>{analysis['performance_categories']['acceptable']['count']}</td>
                            <td>{analysis['performance_categories']['acceptable']['percentage']:.1f}%</td>
                            <td>⚠️ Moderate</td>
                        </tr>
                        <tr class="poor">
                            <td>Poor</td>
                            <td>0.0 - 0.4</td>
                            <td>{analysis['performance_categories']['poor']['count']}</td>
                            <td>{analysis['performance_categories']['poor']['percentage']:.1f}%</td>
                            <td>❌ Needs Improvement</td>
                        </tr>
                    </table>
                    <div class="chart-container" id="performance-categories"></div>
                </div>

                <div class="section">
                    <h2>📋 Composite Metrics Breakdown</h2>
                    <div class="composite-breakdown">
        """

        # Add composite metrics breakdown
        for metric_name, metric_data in analysis['composite_metrics_analysis'].items():
            html_content += f"""
                        <div class="metric-breakdown">
                            <h4>{metric_name.replace('_', ' ').title()}</h4>
                            <p><strong>Mean:</strong> {metric_data['mean']:.4f}</p>
                            <p><strong>Std:</strong> {metric_data['std']:.4f}</p>
                            <p><strong>Range:</strong> {metric_data['min']:.4f} - {metric_data['max']:.4f}</p>
                        </div>
            """

        html_content += f"""
                    </div>
                </div>

                <div class="section">
                    <h2>🎯 Best Performing Cycles</h2>
                    <table class="performance-table">
                        <tr>
                            <th>Rank</th>
                            <th>Cycle</th>
                            <th>Composite Score</th>
                            <th>Return</th>
                            <th>Final Balance</th>
                            <th>Trades</th>
                        </tr>
        """

        # Add best cycles
        for i, cycle in enumerate(analysis['best_cycles'], 1):
            html_content += f"""
                        <tr>
                            <td>{i}</td>
                            <td>{cycle['cycle']}</td>
                            <td>{cycle['testing']['composite_score']:.4f}</td>
                            <td>{cycle['testing']['total_return']:+.2f}%</td>
                            <td>${cycle['testing']['final_balance']:.2f}</td>
                            <td>{cycle['testing']['total_trades']}</td>
                        </tr>
            """

        html_content += f"""
                    </table>
                </div>

                <div class="section">
                    <h2>📊 Statistical Analysis</h2>
                    <div class="chart-container" id="correlation-analysis"></div>
                    <div class="chart-container" id="equity-curves"></div>
                </div>

                <div class="section">
                    <h2>🎯 Key Insights & Recommendations</h2>
                    <div style="background: #e8f4fd; padding: 20px; border-radius: 10px; border-left: 5px solid #3498db;">
                        <h3>Performance Analysis:</h3>
                        <ul>
                            <li><strong>Success Rate:</strong> {analysis['summary']['success_rate']:.1f}% of cycles completed successfully</li>
                            <li><strong>Profitability:</strong> {analysis['returns']['positive_cycles']}/{analysis['summary']['valid_cycles']} cycles were profitable</li>
                            <li><strong>Consistency:</strong> Composite score std deviation of {analysis['composite_scores']['std']:.3f}</li>
                            <li><strong>Best Performance:</strong> Top cycle achieved {analysis['composite_scores']['max']:.3f} composite score</li>
                        </ul>

                        <h3>Recommendations:</h3>
                        <ul>
                            <li>Focus on improving cycles with composite scores below 0.4</li>
                            <li>Analyze top-performing cycles for pattern identification</li>
                            <li>Consider ensemble methods combining best-performing models</li>
                            <li>Implement adaptive learning based on market conditions</li>
                        </ul>
                    </div>
                </div>
            </div>

            <script>
                // Composite Score Distribution
                var compositeData = [{{
                    x: {composite_scores},
                    type: 'histogram',
                    nbinsx: 30,
                    name: 'Composite Scores',
                    marker: {{color: '#3498db'}}
                }}];

                Plotly.newPlot('composite-distribution', compositeData, {{
                    title: 'Distribution of Composite Scores (1000 Cycles)',
                    xaxis: {{title: 'Composite Score'}},
                    yaxis: {{title: 'Frequency'}}
                }});

                // Returns Distribution
                var returnsData = [{{
                    x: {returns},
                    type: 'histogram',
                    nbinsx: 30,
                    name: 'Returns',
                    marker: {{color: '#e74c3c'}}
                }}];

                Plotly.newPlot('returns-distribution', returnsData, {{
                    title: 'Distribution of Returns (1000 Cycles)',
                    xaxis: {{title: 'Return (%)'}},
                    yaxis: {{title: 'Frequency'}}
                }});

                // Performance Categories Pie Chart
                var categoryData = [{{
                    values: [{analysis['performance_categories']['excellent']['count']},
                            {analysis['performance_categories']['good']['count']},
                            {analysis['performance_categories']['acceptable']['count']},
                            {analysis['performance_categories']['poor']['count']}],
                    labels: ['Excellent (0.8+)', 'Good (0.6-0.8)', 'Acceptable (0.4-0.6)', 'Poor (<0.4)'],
                    type: 'pie',
                    marker: {{colors: ['#27ae60', '#f39c12', '#e67e22', '#e74c3c']}}
                }}];

                Plotly.newPlot('performance-categories', categoryData, {{
                    title: 'Performance Category Distribution'
                }});

                // Correlation Analysis
                var correlationData = [{{
                    x: {composite_scores},
                    y: {returns},
                    mode: 'markers',
                    type: 'scatter',
                    name: 'Composite vs Returns',
                    marker: {{color: '#9b59b6', size: 6}}
                }}];

                Plotly.newPlot('correlation-analysis', correlationData, {{
                    title: 'Composite Score vs Returns Correlation',
                    xaxis: {{title: 'Composite Score'}},
                    yaxis: {{title: 'Return (%)'}}
                }});
            </script>
        </body>
        </html>
        """

        return html_content


async def main():
    """Main evaluation function."""
    evaluator = Comprehensive1000CycleEvaluator(initial_capital=300.0)

    # Run comprehensive evaluation
    results = await evaluator.run_comprehensive_evaluation()

    # Analyze results
    analysis = evaluator.analyze_results(results)

    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Save raw results
    results_file = f"reports/1000_cycle_results_{timestamp}.json"
    Path(results_file).parent.mkdir(parents=True, exist_ok=True)

    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)

    # Save analysis
    analysis_file = f"reports/1000_cycle_analysis_{timestamp}.json"
    with open(analysis_file, 'w') as f:
        json.dump(analysis, f, indent=2, default=str)

    # Generate enhanced HTML report
    report_generator = EnhancedHTMLReportGenerator()
    best_cycles = analysis.get('best_cycles', [])
    html_report = report_generator.generate_enhanced_report(analysis, best_cycles, timestamp)
    html_file = f"reports/1000_cycle_enhanced_report_{timestamp}.html"
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(html_report)

    # Print summary
    print("\n🎉 1000-CYCLE EVALUATION COMPLETED!")
    print("=" * 60)
    print(f"📊 Results saved to: {results_file}")
    print(f"📊 Analysis saved to: {analysis_file}")
    print(f"📊 HTML Report saved to: {html_file}")

    if 'error' not in analysis:
        print(f"\n📈 SUMMARY STATISTICS:")
        print(f"   Valid cycles: {analysis['summary']['valid_cycles']}/1000")
        print(f"   Average composite score: {analysis['composite_scores']['mean']:.4f}")
        print(f"   Average return: {analysis['returns']['mean']:+.2f}%")
        print(f"   Positive return cycles: {analysis['returns']['positive_cycles']}")

        print(f"\n🏆 PERFORMANCE CATEGORIES:")
        for category, data in analysis['performance_categories'].items():
            print(f"   {category.capitalize()}: {data['count']} cycles ({data['percentage']:.1f}%)")

        print(f"\n📄 Open the HTML report for detailed analysis:")
        print(f"   {html_file}")

    logger.info("Evaluation completed successfully!")


if __name__ == "__main__":
    asyncio.run(main())
