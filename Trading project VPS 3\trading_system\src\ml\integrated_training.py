"""
Integrated ML Training Pipeline with Validated Trading Logic
Combines TCN-CNN-PPO with real market data and correct risk management
"""
import asyncio
import sys
import numpy as np
import torch
import torch.nn as nn
from datetime import datetime, timedelta, timezone
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import logging

# Fix for Windows event loop issue
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# Add parent directories to path
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.data.binance_fetcher import BinanceDataFetcher
from src.trading.environment import GridTradingEnv, Action
from src.config.settings import settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TradingFeatureExtractor:
    """Extract features from OHLCV data for ML training with specified indicators only."""

    def __init__(self, lookback_window: int = 24):
        self.lookback_window = lookback_window

    def calculate_vwap(self, candles: List, period: int = 20) -> List[float]:
        """Calculate Volume Weighted Average Price."""
        vwap_values = []

        for i in range(len(candles)):
            start_idx = max(0, i - period + 1)
            period_candles = candles[start_idx:i+1]

            if len(period_candles) == 0:
                vwap_values.append(candles[i].close)
                continue

            total_volume = sum(c.volume for c in period_candles)
            if total_volume == 0:
                vwap_values.append(candles[i].close)
                continue

            weighted_price = sum(c.close * c.volume for c in period_candles)
            vwap = weighted_price / total_volume
            vwap_values.append(vwap)

        return vwap_values

    def calculate_rsi(self, candles: List, period: int = 5) -> List[float]:
        """Calculate RSI with 5 period."""
        if len(candles) < period + 1:
            return [50.0] * len(candles)  # Neutral RSI

        rsi_values = []

        for i in range(len(candles)):
            if i < period:
                rsi_values.append(50.0)  # Neutral RSI for insufficient data
                continue

            # Get price changes for the period
            price_changes = []
            for j in range(i - period + 1, i + 1):
                if j > 0:
                    change = candles[j].close - candles[j-1].close
                    price_changes.append(change)

            if len(price_changes) == 0:
                rsi_values.append(50.0)
                continue

            # Calculate gains and losses
            gains = [change for change in price_changes if change > 0]
            losses = [-change for change in price_changes if change < 0]

            avg_gain = sum(gains) / len(price_changes) if gains else 0
            avg_loss = sum(losses) / len(price_changes) if losses else 0

            if avg_loss == 0:
                rsi = 100.0
            else:
                rs = avg_gain / avg_loss
                rsi = 100 - (100 / (1 + rs))

            rsi_values.append(rsi)

        return rsi_values

    def calculate_bollinger_bands(self, candles: List, period: int = 20, std_dev: float = 2.0) -> tuple:
        """Calculate Bollinger Bands."""
        bb_upper = []
        bb_middle = []
        bb_lower = []
        bb_position = []  # Position within bands (0-1)

        for i in range(len(candles)):
            start_idx = max(0, i - period + 1)
            period_candles = candles[start_idx:i+1]

            if len(period_candles) < 2:
                # Insufficient data
                price = candles[i].close
                bb_upper.append(price * 1.02)
                bb_middle.append(price)
                bb_lower.append(price * 0.98)
                bb_position.append(0.5)
                continue

            # Calculate SMA
            prices = [c.close for c in period_candles]
            sma = sum(prices) / len(prices)

            # Calculate standard deviation
            variance = sum((price - sma) ** 2 for price in prices) / len(prices)
            std = variance ** 0.5

            # Calculate bands
            upper = sma + (std_dev * std)
            lower = sma - (std_dev * std)

            # Calculate position within bands
            current_price = candles[i].close
            if upper == lower:
                position = 0.5
            else:
                position = (current_price - lower) / (upper - lower)
                position = max(0, min(1, position))  # Clamp to 0-1

            bb_upper.append(upper)
            bb_middle.append(sma)
            bb_lower.append(lower)
            bb_position.append(position)

        return bb_upper, bb_middle, bb_lower, bb_position

    async def get_eth_btc_ratio(self) -> float:
        """Get current ETH/BTC ratio."""
        try:
            from src.data.binance_fetcher import BinanceDataFetcher

            async with BinanceDataFetcher() as fetcher:
                # Get current ETH and BTC prices in USDT
                eth_response = await fetcher.fetch_klines("ETHUSDT", "1h", limit=1)
                btc_response = await fetcher.fetch_klines("BTCUSDT", "1h", limit=1)

                if eth_response.success and btc_response.success:
                    eth_price = eth_response.data[-1].close
                    btc_price = btc_response.data[-1].close
                    ratio = eth_price / btc_price
                    return ratio
                else:
                    return 0.065  # Default ETH/BTC ratio
        except:
            return 0.065  # Default ETH/BTC ratio

    def extract_features(self, candles: List, eth_btc_ratio: float = None) -> np.ndarray:
        """Extract features with only the 4 specified indicators."""
        if len(candles) < self.lookback_window:
            raise ValueError(f"Need at least {self.lookback_window} candles")

        # Use last lookback_window candles
        recent_candles = candles[-self.lookback_window:]

        # Calculate indicators for all candles (need history for proper calculation)
        all_candles = candles[-max(50, self.lookback_window):]  # Use more history for indicators

        # Calculate indicators
        vwap_values = self.calculate_vwap(all_candles)
        rsi_values = self.calculate_rsi(all_candles)
        bb_upper, bb_middle, bb_lower, bb_position = self.calculate_bollinger_bands(all_candles)

        # Get the values for our lookback window
        start_idx = len(all_candles) - self.lookback_window
        vwap_window = vwap_values[start_idx:]
        rsi_window = rsi_values[start_idx:]
        bb_pos_window = bb_position[start_idx:]

        # Use provided ETH/BTC ratio or default
        if eth_btc_ratio is None:
            eth_btc_ratio = 0.065  # Default ratio

        features = []
        for i, candle in enumerate(recent_candles):
            # Normalize prices relative to current close for better scaling
            current_close = candle.close

            candle_features = [
                # 1. Basic OHLCV (normalized)
                candle.open / current_close,
                candle.high / current_close,
                candle.low / current_close,
                1.0,  # close / close = 1.0
                candle.volume / 1000000,  # Normalize volume

                # 2. VWAP indicator
                vwap_window[i] / current_close,

                # 3. RSI (5 period) - already 0-100, normalize to 0-1
                rsi_window[i] / 100.0,

                # 4. Bollinger Bands position (already 0-1)
                bb_pos_window[i],

                # 5. ETH/BTC ratio (normalized)
                eth_btc_ratio / 0.1  # Normalize around typical 0.065 ratio
            ]

            features.extend(candle_features)

        return np.array(features, dtype=np.float32)

    def get_feature_dim(self) -> int:
        """Get the dimension of extracted features."""
        return self.lookback_window * 9  # 9 features per candle


class SimpleTCNPPO(nn.Module):
    """Simplified TCN-PPO model for trading decisions."""

    def __init__(self, feature_dim: int, hidden_dim: int = 128):
        super().__init__()
        self.feature_dim = feature_dim

        # Feature processing
        self.feature_net = nn.Sequential(
            nn.Linear(feature_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2)
        )

        # Policy head (3 actions: BUY, SELL, HOLD)
        self.policy_head = nn.Sequential(
            nn.Linear(hidden_dim, 64),
            nn.ReLU(),
            nn.Linear(64, 3)
        )

        # Value head
        self.value_head = nn.Sequential(
            nn.Linear(hidden_dim, 64),
            nn.ReLU(),
            nn.Linear(64, 1)
        )

        # Initialize weights
        self.apply(self._init_weights)

    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            nn.init.orthogonal_(module.weight, gain=0.01)
            if module.bias is not None:
                nn.init.constant_(module.bias, 0)

    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        features = self.feature_net(x)
        logits = self.policy_head(features)
        value = self.value_head(features).squeeze(-1)
        return logits, value

    def act(self, x: torch.Tensor, deterministic: bool = False) -> Tuple[int, float, float]:
        """Select action given state."""
        logits, value = self.forward(x)
        probs = torch.softmax(logits, dim=-1)

        if deterministic:
            action = torch.argmax(probs, dim=-1)
        else:
            dist = torch.distributions.Categorical(probs)
            action = dist.sample()

        log_prob = torch.log(probs[0, action.item()] + 1e-10)
        return action.item(), log_prob.item(), value.item()


class CompositeMetricsCalculator:
    """Calculate composite performance metrics for training optimization."""

    def __init__(self):
        # Composite weights (must sum to 1.0)
        self.weights = {
            'win_rate': 0.22,        # 22% weight (target > 55%)
            'equity_growth': 0.20,   # 20% weight (compounding monthly)
            'sortino_ratio': 0.18,   # 18% weight (target > 2.0)
            'calmar_ratio': 0.15,    # 15% weight (target > 2.0)
            'profit_factor': 0.10,   # 10% weight (target > 1.5)
            'max_drawdown': 0.08,    # 8% weight (limit 15%)
            'risk_of_ruin': 0.05,    # 5% weight (target < 1%)
            'trade_frequency': 0.02  # 2% weight (2-5 trades/day)
        }

        # Target values for normalization
        self.targets = {
            'win_rate': 0.55,        # 55%
            'equity_growth': 0.10,   # 10% monthly
            'sortino_ratio': 2.0,    # 2.0
            'calmar_ratio': 2.0,     # 2.0
            'profit_factor': 1.5,    # 1.5
            'max_drawdown': 0.15,    # 15% (lower is better)
            'risk_of_ruin': 0.01,    # 1% (lower is better)
            'trade_frequency': 3.5   # 3.5 trades/day (middle of 2-5 range)
        }

    def calculate_metrics(self, trades: List, equity_curve: List, initial_balance: float,
                         days_elapsed: float) -> Dict:
        """Calculate all composite metrics."""
        if not trades or len(equity_curve) < 2:
            return self._default_metrics()

        # Basic calculations
        final_balance = equity_curve[-1]
        total_return = (final_balance / initial_balance) - 1

        # 1. Win Rate
        winning_trades = [t for t in trades if t.get('pnl', 0) > 0]
        win_rate = len(winning_trades) / len(trades) if trades else 0

        # 2. Equity Growth (annualized)
        if days_elapsed > 0:
            daily_return = (final_balance / initial_balance) ** (1 / days_elapsed) - 1
            monthly_return = (1 + daily_return) ** 30 - 1
        else:
            monthly_return = 0

        # 3. Sortino Ratio
        returns = []
        for i in range(1, len(equity_curve)):
            ret = (equity_curve[i] / equity_curve[i-1]) - 1
            returns.append(ret)

        if returns:
            avg_return = np.mean(returns)
            downside_returns = [r for r in returns if r < 0]
            downside_std = np.std(downside_returns) if downside_returns else 0.001
            sortino_ratio = avg_return / downside_std if downside_std > 0 else 0
        else:
            sortino_ratio = 0

        # 4. Max Drawdown
        peak = initial_balance
        max_drawdown = 0
        for equity in equity_curve:
            if equity > peak:
                peak = equity
            drawdown = (peak - equity) / peak
            if drawdown > max_drawdown:
                max_drawdown = drawdown

        # 5. Calmar Ratio
        if max_drawdown > 0:
            calmar_ratio = abs(total_return) / max_drawdown
        else:
            calmar_ratio = 0

        # 6. Profit Factor
        gross_profit = sum(t.get('pnl', 0) for t in trades if t.get('pnl', 0) > 0)
        gross_loss = abs(sum(t.get('pnl', 0) for t in trades if t.get('pnl', 0) < 0))
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else 1.0

        # 7. Risk of Ruin (simplified Kelly criterion based)
        if trades:
            win_prob = win_rate
            avg_win = np.mean([t.get('pnl', 0) for t in trades if t.get('pnl', 0) > 0]) if winning_trades else 0
            avg_loss = abs(np.mean([t.get('pnl', 0) for t in trades if t.get('pnl', 0) < 0])) if len(trades) > len(winning_trades) else 1

            if avg_loss > 0:
                win_loss_ratio = avg_win / avg_loss
                kelly_f = win_prob - ((1 - win_prob) / win_loss_ratio)
                risk_of_ruin = max(0, min(1, 1 - kelly_f)) if kelly_f > 0 else 0.5
            else:
                risk_of_ruin = 0.01
        else:
            risk_of_ruin = 0.5

        # 8. Trade Frequency (trades per day)
        trade_frequency = len(trades) / max(days_elapsed, 1) if days_elapsed > 0 else 0

        return {
            'win_rate': win_rate,
            'equity_growth': monthly_return,
            'sortino_ratio': sortino_ratio,
            'calmar_ratio': calmar_ratio,
            'profit_factor': profit_factor,
            'max_drawdown': max_drawdown,
            'risk_of_ruin': risk_of_ruin,
            'trade_frequency': trade_frequency
        }

    def normalize_metric(self, metric_name: str, value: float) -> float:
        """Normalize metric to 0-1 range where 1 is best."""
        # Ensure value is real (not complex)
        if isinstance(value, complex):
            value = value.real

        # Ensure value is finite
        if not np.isfinite(value):
            value = 0.0

        target = self.targets[metric_name]

        if metric_name in ['max_drawdown', 'risk_of_ruin']:
            # Lower is better - invert the score
            if value <= 0:
                return 1.0
            elif value >= target * 2:  # Twice the target is considered 0
                return 0.0
            else:
                return 1.0 - (value / (target * 2))

        elif metric_name == 'trade_frequency':
            # Optimal range is 2-5 trades/day, target is 3.5
            if 2 <= value <= 5:
                # Within optimal range, score based on distance from target
                distance = abs(value - target)
                return 1.0 - (distance / 1.5)  # Max distance is 1.5 from target
            elif value < 2:
                # Too few trades
                return value / 2.0
            else:
                # Too many trades (value > 5)
                return max(0, 1.0 - ((value - 5) / 5))

        else:
            # Higher is better
            if value <= 0:
                return 0.0
            elif value >= target:
                return 1.0
            else:
                return value / target

    def calculate_composite_score(self, metrics: Dict) -> float:
        """Calculate weighted composite score (0-1, where 1 is best)."""
        total_score = 0.0

        for metric_name, weight in self.weights.items():
            if metric_name in metrics:
                normalized_score = self.normalize_metric(metric_name, metrics[metric_name])
                weighted_score = normalized_score * weight
                total_score += weighted_score

        return min(1.0, max(0.0, total_score))  # Clamp to 0-1

    def _default_metrics(self) -> Dict:
        """Return default metrics for cases with no data."""
        return {
            'win_rate': 0.0,
            'equity_growth': 0.0,
            'sortino_ratio': 0.0,
            'calmar_ratio': 0.0,
            'profit_factor': 1.0,
            'max_drawdown': 0.0,
            'risk_of_ruin': 0.5,
            'trade_frequency': 0.0
        }


class IntegratedTrainingPipeline:
    """Complete training pipeline integrating ML with validated trading logic."""

    def __init__(self, initial_balance: float = 300.0):
        self.initial_balance = initial_balance
        self.feature_extractor = TradingFeatureExtractor(lookback_window=24)
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.metrics_calculator = CompositeMetricsCalculator()

        # Initialize model
        feature_dim = self.feature_extractor.get_feature_dim()
        self.model = SimpleTCNPPO(feature_dim).to(self.device)
        self.optimizer = torch.optim.Adam(self.model.parameters(), lr=3e-4)

        logger.info(f"Initialized model with {feature_dim} features on {self.device}")
        logger.info("Training optimized for composite metrics with weights:")
        for metric, weight in self.metrics_calculator.weights.items():
            logger.info(f"  {metric}: {weight:.1%}")

    async def collect_training_data(self, days: int = 30) -> List:
        """Collect real market data for training."""
        logger.info(f"Collecting {days} days of real market data...")

        async with BinanceDataFetcher() as fetcher:
            end_time = datetime.now(timezone.utc)
            start_time = end_time - timedelta(days=days)

            response = await fetcher.fetch_historical_data(
                symbol="BTCUSDT",
                interval="1h",
                start_time=start_time,
                end_time=end_time
            )

            if not response.success:
                raise RuntimeError(f"Failed to fetch data: {response.error}")

            logger.info(f"Collected {len(response.data)} candles")
            return response.data

    async def simulate_episode(self, candles: List, model: SimpleTCNPPO = None) -> Dict:
        """Simulate a trading episode with composite reward calculation."""
        env = GridTradingEnv(
            initial_balance=self.initial_balance,
            risk_per_trade=0.05,
            grid_spacing=0.0025,
            take_profit_multiplier=2.0,
            fee_rate=0.001
        )

        # Reset environment
        env.reset(candles[0].close, candles[0].timestamp)

        # Get ETH/BTC ratio for this episode
        eth_btc_ratio = await self.feature_extractor.get_eth_btc_ratio()

        episode_data = {
            'states': [],
            'actions': [],
            'rewards': [],
            'log_probs': [],
            'values': [],
            'dones': [],
            'equity_curve': [self.initial_balance],
            'trades': []
        }

        start_time = candles[0].timestamp

        for i in range(self.feature_extractor.lookback_window, len(candles)):
            # Extract features with ETH/BTC ratio
            try:
                features = self.feature_extractor.extract_features(candles[:i+1], eth_btc_ratio)
                state_tensor = torch.FloatTensor(features).unsqueeze(0).to(self.device)

                # Get action from model or random
                if model is not None:
                    action_idx, log_prob, value = model.act(state_tensor)
                else:
                    # Random baseline
                    action_idx = np.random.choice(3)
                    log_prob = np.log(1/3)
                    value = 0.0

                # Convert to environment action
                action_map = {0: Action.BUY, 1: Action.SELL, 2: Action.HOLD}
                action = action_map[action_idx]

                # Execute step
                candle = candles[i]
                prev_positions = len(env.closed_positions)
                _, _, _, _ = env.step(action, candle.close, candle.timestamp, candle)

                # Track equity curve
                episode_data['equity_curve'].append(env.equity)

                # Track completed trades
                if len(env.closed_positions) > prev_positions:
                    latest_trade = env.closed_positions[-1]
                    episode_data['trades'].append({
                        'pnl': latest_trade.pnl,
                        'entry_time': latest_trade.entry_time,
                        'exit_time': candle.timestamp
                    })

                # Store episode data (reward will be calculated at end)
                episode_data['states'].append(features)
                episode_data['actions'].append(action_idx)
                episode_data['log_probs'].append(log_prob)
                episode_data['values'].append(value)
                episode_data['dones'].append(False)

            except ValueError:
                continue

        # Calculate composite metrics and rewards
        end_time = candles[-1].timestamp
        days_elapsed = (end_time - start_time).total_seconds() / (24 * 3600)

        # Calculate composite metrics
        composite_metrics = self.metrics_calculator.calculate_metrics(
            episode_data['trades'],
            episode_data['equity_curve'],
            self.initial_balance,
            days_elapsed
        )

        # Calculate composite score (0-1, where 1 is best)
        composite_score = self.metrics_calculator.calculate_composite_score(composite_metrics)

        # Create rewards based on composite score
        # Distribute the composite score across all actions in the episode
        num_actions = len(episode_data['actions'])
        if num_actions > 0:
            base_reward = composite_score / num_actions

            # Shape rewards: give higher rewards to trading actions if they led to good outcomes
            shaped_rewards = []
            for i, action_idx in enumerate(episode_data['actions']):
                reward = base_reward

                # Bonus for trading actions if composite score is good
                if action_idx != 2 and composite_score > 0.5:  # Not HOLD and good performance
                    reward *= 1.2

                # Penalty for excessive trading if frequency is too high
                if composite_metrics['trade_frequency'] > 5:
                    if action_idx != 2:  # Trading action
                        reward *= 0.8

                shaped_rewards.append(reward)

            episode_data['rewards'] = shaped_rewards
        else:
            episode_data['rewards'] = []

        # Final episode data
        episode_data.update({
            'final_balance': env.balance,
            'total_return': (env.balance / self.initial_balance - 1) * 100,
            'composite_score': composite_score,
            'composite_metrics': composite_metrics,
            'total_reward': sum(episode_data['rewards']) if episode_data['rewards'] else 0
        })

        return episode_data

    async def simulate_episode_detailed(self, candles: List, model: SimpleTCNPPO = None) -> Dict:
        """Simulate a trading episode with detailed trade-by-trade tracking."""
        env = GridTradingEnv(
            initial_balance=self.initial_balance,
            risk_per_trade=0.05,
            grid_spacing=0.0025,
            take_profit_multiplier=2.0,
            fee_rate=0.001
        )

        # Reset environment
        env.reset(candles[0].close, candles[0].timestamp)

        # Get ETH/BTC ratio for this episode
        eth_btc_ratio = await self.feature_extractor.get_eth_btc_ratio()

        episode_data = {
            'states': [],
            'actions': [],
            'rewards': [],
            'log_probs': [],
            'values': [],
            'dones': [],
            'equity_curve': [self.initial_balance],
            'trades': [],
            'detailed_trades': [],
            'trade_actions': [],
            'commission_paid': 0.0
        }

        start_time = candles[0].timestamp

        for i in range(self.feature_extractor.lookback_window, len(candles)):
            # Extract features with ETH/BTC ratio
            try:
                features = self.feature_extractor.extract_features(candles[:i+1], eth_btc_ratio)
                state_tensor = torch.FloatTensor(features).unsqueeze(0).to(self.device)

                # Get action from model or random
                if model is not None:
                    action_idx, log_prob, value = model.act(state_tensor)
                else:
                    # Random baseline
                    action_idx = np.random.choice(3)
                    log_prob = np.log(1/3)
                    value = 0.0

                # Convert to environment action
                action_map = {0: Action.BUY, 1: Action.SELL, 2: Action.HOLD}
                action = action_map[action_idx]

                # Execute step
                candle = candles[i]
                prev_positions = len(env.closed_positions)
                prev_balance = env.balance

                # Record action details
                action_detail = {
                    'timestamp': candle.timestamp,
                    'candle_index': i,
                    'price': candle.close,
                    'action': action.name,
                    'balance_before': env.balance,
                    'equity_before': env.equity,
                    'open_positions': len(env.positions)
                }

                _, _, _, _ = env.step(action, candle.close, candle.timestamp, candle)

                # Record action results
                action_detail.update({
                    'balance_after': env.balance,
                    'equity_after': env.equity,
                    'balance_change': env.balance - prev_balance,
                    'open_positions_after': len(env.positions)
                })

                # Track equity curve
                episode_data['equity_curve'].append(env.equity)

                # Track completed trades with full details
                if len(env.closed_positions) > prev_positions:
                    latest_trade = env.closed_positions[-1]

                    # Use the simple model data directly from the position
                    gross_pnl = latest_trade.gross_pnl  # Exact target amount ($30 or -$15)
                    total_commission = latest_trade.commission  # 0.1% of risk amount
                    net_pnl = latest_trade.pnl  # Net P&L after commission

                    episode_data['commission_paid'] += total_commission

                    # Use risk amount as trade size for simple model
                    trade_size = latest_trade.target_risk  # Risk amount ($15, $16.50, etc.)

                    trade_detail = {
                        'trade_id': len(episode_data['detailed_trades']) + 1,
                        'entry_time': latest_trade.entry_time,
                        'exit_time': candle.timestamp,
                        'direction': latest_trade.direction.name,
                        'entry_price': latest_trade.entry_price,
                        'exit_price': candle.close,
                        'size': latest_trade.size,
                        'pnl_gross': gross_pnl,
                        'commission': total_commission,
                        'pnl_net': net_pnl,
                        'trade_size': trade_size,
                        'return_pct': (net_pnl / trade_size) * 100 if trade_size > 0 else 0,
                        'duration_hours': (candle.timestamp - latest_trade.entry_time).total_seconds() / 3600,
                        'exit_reason': 'TAKE_PROFIT' if net_pnl > 0 else 'STOP_LOSS',
                        'account_balance': env.balance  # Add account balance after trade
                    }

                    episode_data['detailed_trades'].append(trade_detail)
                    episode_data['trades'].append({
                        'pnl': net_pnl,  # Net PnL after commission
                        'entry_time': latest_trade.entry_time,
                        'exit_time': candle.timestamp
                    })

                episode_data['trade_actions'].append(action_detail)

                # Store episode data (reward will be calculated at end)
                episode_data['states'].append(features)
                episode_data['actions'].append(action_idx)
                episode_data['log_probs'].append(log_prob)
                episode_data['values'].append(value)
                episode_data['dones'].append(False)

            except ValueError:
                continue

        # Calculate composite metrics and rewards
        end_time = candles[-1].timestamp
        days_elapsed = (end_time - start_time).total_seconds() / (24 * 3600)

        # Calculate composite metrics
        composite_metrics = self.metrics_calculator.calculate_metrics(
            episode_data['trades'],
            episode_data['equity_curve'],
            self.initial_balance,
            days_elapsed
        )

        # Calculate composite score (0-1, where 1 is best)
        composite_score = self.metrics_calculator.calculate_composite_score(composite_metrics)

        # Create rewards based on composite score
        num_actions = len(episode_data['actions'])
        if num_actions > 0:
            base_reward = composite_score / num_actions

            # Shape rewards: give higher rewards to trading actions if they led to good outcomes
            shaped_rewards = []
            for i, action_idx in enumerate(episode_data['actions']):
                reward = base_reward

                # Bonus for trading actions if composite score is good
                if action_idx != 2 and composite_score > 0.5:  # Not HOLD and good performance
                    reward *= 1.2

                # Penalty for excessive trading if frequency is too high
                if composite_metrics['trade_frequency'] > 5:
                    if action_idx != 2:  # Trading action
                        reward *= 0.8

                shaped_rewards.append(reward)

            episode_data['rewards'] = shaped_rewards
        else:
            episode_data['rewards'] = []

        # Final episode data
        episode_data.update({
            'final_balance': env.balance,
            'total_return': (env.balance / self.initial_balance - 1) * 100,
            'composite_score': composite_score,
            'composite_metrics': composite_metrics,
            'total_reward': sum(episode_data['rewards']) if episode_data['rewards'] else 0,
            'total_commission': episode_data['commission_paid']
        })

        return episode_data

    async def train_model(self, episodes: int = 100, save_path: str = "models/tcn_ppo_trading.pth"):
        """Train the TCN-PPO model on real market data."""
        logger.info(f"Starting training for {episodes} episodes...")

        # Collect training data
        training_data = await self.collect_training_data(days=90)  # 90 days of data

        best_reward = float('-inf')
        training_history = []

        for episode in range(episodes):
            # Simulate episode
            episode_data = await self.simulate_episode(training_data, self.model)

            # Calculate returns and advantages (simplified)
            rewards = episode_data['rewards']
            values = episode_data['values']

            returns = []
            running_return = 0
            for reward in reversed(rewards):
                running_return = reward + 0.99 * running_return
                returns.insert(0, running_return)

            returns = torch.FloatTensor(returns).to(self.device)
            values = torch.FloatTensor(values).to(self.device)
            advantages = returns - values

            # Normalize advantages
            advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)

            # Update model
            states = torch.FloatTensor(np.array(episode_data['states'])).to(self.device)
            actions = torch.LongTensor(episode_data['actions']).to(self.device)
            old_log_probs = torch.FloatTensor(episode_data['log_probs']).to(self.device)

            # Forward pass
            logits, new_values = self.model(states)
            probs = torch.softmax(logits, dim=-1)
            dist = torch.distributions.Categorical(probs)
            new_log_probs = dist.log_prob(actions)
            entropy = dist.entropy()

            # PPO loss
            ratio = (new_log_probs - old_log_probs).exp()
            surr1 = ratio * advantages
            surr2 = torch.clamp(ratio, 0.8, 1.2) * advantages
            policy_loss = -torch.min(surr1, surr2).mean()

            value_loss = nn.MSELoss()(new_values, returns)
            entropy_loss = -entropy.mean()

            total_loss = policy_loss + 0.5 * value_loss + 0.01 * entropy_loss

            # Backward pass
            self.optimizer.zero_grad()
            total_loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), 0.5)
            self.optimizer.step()

            # Logging with composite metrics
            total_reward = episode_data['total_reward']
            final_balance = episode_data['final_balance']
            total_return = episode_data['total_return']
            composite_score = episode_data['composite_score']
            composite_metrics = episode_data['composite_metrics']

            training_history.append({
                'episode': episode,
                'total_reward': total_reward,
                'final_balance': final_balance,
                'total_return': total_return,
                'composite_score': composite_score,
                'composite_metrics': composite_metrics,
                'policy_loss': policy_loss.item(),
                'value_loss': value_loss.item(),
                'entropy_loss': entropy_loss.item()
            })

            if episode % 10 == 0:
                logger.info(f"Episode {episode}: Composite={composite_score:.4f}, "
                           f"Balance=${final_balance:.2f}, WinRate={composite_metrics['win_rate']:.1%}, "
                           f"Trades={len(episode_data['trades'])}")

            # Save best model based on composite score
            if composite_score > best_reward:
                best_reward = composite_score
                Path(save_path).parent.mkdir(parents=True, exist_ok=True)
                torch.save({
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'episode': episode,
                    'best_reward': best_reward,
                    'training_history': training_history
                }, save_path)
                logger.info(f"Saved new best model with reward {best_reward:.4f}")

        logger.info("Training completed!")
        return training_history

    async def quick_training_update(self, episode_data: Dict):
        """Quick training update for 1000-cycle evaluation."""
        if not episode_data['states'] or len(episode_data['states']) < 2:
            return

        try:
            # Convert episode data to tensors
            states = torch.FloatTensor(np.array(episode_data['states'])).to(self.device)
            actions = torch.LongTensor(episode_data['actions']).to(self.device)
            rewards = torch.FloatTensor(episode_data['rewards']).to(self.device)
            old_log_probs = torch.FloatTensor(episode_data['log_probs']).to(self.device)

            # Normalize rewards
            if len(rewards) > 1:
                rewards = (rewards - rewards.mean()) / (rewards.std() + 1e-8)

            # Forward pass
            logits, values = self.model(states)
            probs = torch.softmax(logits, dim=-1)
            dist = torch.distributions.Categorical(probs)
            new_log_probs = dist.log_prob(actions)
            entropy = dist.entropy()

            # PPO loss
            ratio = (new_log_probs - old_log_probs).exp()
            surr1 = ratio * rewards
            surr2 = torch.clamp(ratio, 0.9, 1.1) * rewards
            policy_loss = -torch.min(surr1, surr2).mean()

            value_loss = nn.MSELoss()(values, rewards)
            entropy_loss = -entropy.mean()

            total_loss = policy_loss + 0.5 * value_loss + 0.01 * entropy_loss

            # Backward pass
            self.optimizer.zero_grad()
            total_loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), 0.5)
            self.optimizer.step()

        except Exception as e:
            logger.warning(f"Quick training update failed: {e}")


async def main():
    """Main training function."""
    logger.info("🚀 Starting Integrated ML Training Pipeline")
    logger.info("=" * 60)

    # Initialize training pipeline
    pipeline = IntegratedTrainingPipeline(initial_balance=10000.0)

    # Train model
    history = await pipeline.train_model(episodes=50)

    logger.info("✅ Training completed successfully!")
    logger.info(f"📊 Trained for {len(history)} episodes")
    logger.info(f"🎯 Best reward: {max(h['total_reward'] for h in history):.4f}")


if __name__ == "__main__":
    asyncio.run(main())
