import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import List, Tuple

class TCNBlock(nn.Module):
    """Temporal Convolutional Network block with residual connection."""
    def __init__(self, in_channels: int, out_channels: int, kernel_size: int, dilation: int):
        super().__init__()
        self.conv = nn.Conv1d(
            in_channels, out_channels, kernel_size,
            padding=(kernel_size-1) * dilation, dilation=dilation
        )
        self.bn = nn.BatchNorm1d(out_channels)
        self.relu = nn.ReLU()
        self.downsample = nn.Conv1d(in_channels, out_channels, 1) if in_channels != out_channels else None

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        residual = x
        out = self.conv(x)
        out = self.bn(out)
        out = self.relu(out)
        # Remove extra padding
        out = out[..., :x.shape[-1]]
        if self.downsample is not None:
            residual = self.downsample(x)
        return out + residual

class TCN_CNN_PPO(nn.Module):
    """
    TCN-CNN model for feature extraction with PPO policy and value heads.
    """
    def __init__(
        self,
        obs_dim: int,
        action_dim: int,
        tcn_channels: List[int] = [32, 64, 128],
        cnn_filters: List[int] = [16, 32],
        kernel_size: int = 3,
        dropout: float = 0.2
    ):
        super().__init__()
        self.obs_dim = obs_dim
        self.action_dim = action_dim
        
        # TCN layers
        tcn_layers = []
        in_channels = obs_dim
        for out_channels in tcn_channels:
            tcn_layers.append(TCNBlock(in_channels, out_channels, kernel_size, dilation=2**len(tcn_layers)))
            in_channels = out_channels
        self.tcn = nn.Sequential(*tcn_layers)
        
        # CNN layers
        cnn_layers = []
        in_channels = tcn_channels[-1]
        for out_channels in cnn_filters:
            cnn_layers.extend([
                nn.Conv1d(in_channels, out_channels, kernel_size=3, padding=1),
                nn.BatchNorm1d(out_channels),
                nn.ReLU(),
                nn.MaxPool1d(2),
                nn.Dropout(dropout)
            ])
            in_channels = out_channels
        self.cnn = nn.Sequential(*cnn_layers)
        
        # Policy and value heads
        self.policy_head = nn.Sequential(
            nn.Linear(in_channels, 64),
            nn.ReLU(),
            nn.Linear(64, action_dim)
        )
        self.value_head = nn.Sequential(
            nn.Linear(in_channels, 64),
            nn.ReLU(),
            nn.Linear(64, 1)
        )
        
        # Initialize weights
        self.apply(self._init_weights)
    
    def _init_weights(self, module):
        if isinstance(module, (nn.Linear, nn.Conv1d)):
            nn.init.orthogonal_(module.weight, gain=0.01)
            if module.bias is not None:
                nn.init.constant_(module.bias, 0)
    
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        # x shape: (batch, seq_len, obs_dim)
        x = x.transpose(1, 2)  # -> (batch, obs_dim, seq_len)
        
        # TCN feature extraction
        x = self.tcn(x)
        
        # CNN processing
        x = self.cnn(x)
        
        # Global average pooling
        x = F.adaptive_avg_pool1d(x, 1).squeeze(-1)
        
        # Policy and value heads
        logits = self.policy_head(x)
        value = self.value_head(x).squeeze(-1)
        
        return logits, value
    
    def act(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """Sample action from policy distribution."""
        with torch.no_grad():
            logits, value = self.forward(x)
            probs = F.softmax(logits, dim=-1)
            dist = torch.distributions.Categorical(probs)
            action = dist.sample()
            log_prob = dist.log_prob(action)
            
        return action, log_prob, value
    
    def evaluate_actions(
        self, 
        x: torch.Tensor, 
        actions: torch.Tensor
    ) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """Evaluate actions for PPO update."""
        logits, value = self.forward(x)
        probs = F.softmax(logits, dim=-1)
        dist = torch.distributions.Categorical(probs)
        log_prob = dist.log_prob(actions)
        entropy = dist.entropy()
        return log_prob, entropy, value
