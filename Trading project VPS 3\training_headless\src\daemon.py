"""
Grid Trading System Daemon
Runs continuously on VPS with email alerts
"""
import os
import time
import signal
import logging
import smtplib
from email.mime.text import MIMEText
from datetime import datetime, timedelta
from dotenv import load_dotenv
from data.binance_data_handler import BinanceDataHandler

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('trading_daemon.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TradingDaemon:
    def __init__(self):
        """Initialize the trading daemon"""
        self.running = False
        self.load_config()
        self.setup_signal_handlers()
        self.data_handler = None
        
    def load_config(self):
        """Load configuration from environment variables"""
        env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), '.env')
        load_dotenv(env_path)
        
        self.config = {
            'email': {
                'enabled': os.getenv('EMAIL_ENABLED', 'false').lower() == 'true',
                'smtp_server': os.getenv('SMTP_SERVER', 'smtp.gmail.com'),
                'smtp_port': int(os.getenv('SMTP_PORT', 587)),
                'username': os.getenv('EMAIL_USERNAME'),
                'password': os.getenv('EMAIL_PASSWORD'),
                'recipient': os.getenv('EMAIL_RECIPIENT')
            },
            'trading': {
                'symbols': ['BTCUSDT'],
                'check_interval': int(os.getenv('CHECK_INTERVAL', 300)),  # 5 minutes
                'max_retries': 3,
                'retry_delay': 60  # seconds
            }
        }
        
    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"Received signal {signum}, shutting down...")
        self.running = False
    
    def send_alert(self, subject: str, message: str):
        """Send email alert"""
        if not self.config['email']['enabled']:
            return
            
        try:
            msg = MIMEText(message)
            msg['Subject'] = f"[Trading Bot] {subject}"
            msg['From'] = self.config['email']['username']
            msg['To'] = self.config['email']['recipient']
            
            with smtplib.SMTP(
                self.config['email']['smtp_server'], 
                self.config['email']['smtp_port']
            ) as server:
                server.starttls()
                server.login(
                    self.config['email']['username'], 
                    self.config['email']['password']
                )
                server.send_message(msg)
                
            logger.info(f"Alert sent: {subject}")
            
        except Exception as e:
            logger.error(f"Failed to send alert: {e}")
    
    def check_market_conditions(self):
        """Check market conditions and execute trades"""
        try:
            # Update market data
            self.data_handler.update_all_data()
            
            # Get latest data
            train_data, test_data = self.data_handler.get_train_test_data()
            
            if train_data.empty or test_data.empty:
                logger.warning("Insufficient data for analysis")
                return
                
            # Example: Simple RSI strategy
            latest_rsi = test_data['rsi'].iloc[-1]
            
            if latest_rsi < 30:
                self.send_alert(
                    "Oversold Condition", 
                    f"RSI at {latest_rsi:.2f} - Consider buying opportunity"
                )
            elif latest_rsi > 70:
                self.send_alert(
                    "Overbought Condition", 
                    f"RSI at {latest_rsi:.2f} - Consider taking profits"
                )
                
        except Exception as e:
            logger.error(f"Error checking market conditions: {e}")
            raise
    
    def run(self):
        """Main daemon loop"""
        logger.info("Starting trading daemon...")
        
        # Initialize data handler
        api_key = os.getenv('BINANCE_API_KEY')
        api_secret = os.getenv('BINANCE_API_SECRET')
        
        if not api_key or not api_secret:
            logger.error("Missing Binance API credentials")
            return
            
        self.data_handler = BinanceDataHandler(api_key, api_secret)
        self.running = True
        
        # Initial data load
        try:
            logger.info("Performing initial data load...")
            self.data_handler.update_all_data()
            logger.info("Initial data load complete")
            
        except Exception as e:
            logger.error(f"Initial data load failed: {e}")
            self.send_alert("Startup Failed", f"Initial data load failed: {e}")
            return
        
        # Main loop
        logger.info("Daemon running. Press Ctrl+C to stop.")
        
        while self.running:
            try:
                start_time = time.time()
                
                # Check market conditions
                self.check_market_conditions()
                
                # Calculate sleep time
                elapsed = time.time() - start_time
                sleep_time = max(0, self.config['trading']['check_interval'] - elapsed)
                
                # Sleep until next check
                for _ in range(int(sleep_time)):
                    if not self.running:
                        break
                    time.sleep(1)
                    
            except KeyboardInterrupt:
                self.running = False
                break
                
            except Exception as e:
                logger.error(f"Error in main loop: {e}")
                self.send_alert("Runtime Error", f"An error occurred: {e}")
                time.sleep(60)  # Prevent tight loop on errors
        
        logger.info("Trading daemon stopped")


def main():
    """Main entry point"""
    daemon = TradingDaemon()
    daemon.run()

if __name__ == "__main__":
    main()
