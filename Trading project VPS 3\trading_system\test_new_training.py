"""
Test New Training with Updated Indicators
Quick test to validate the new indicators work in training
"""
import asyncio
import sys
from pathlib import Path

# Fix for Windows event loop issue
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# Add src to path
sys.path.append(str(Path(__file__).parent))

from src.ml.integrated_training import IntegratedTrainingPipeline


async def test_new_training():
    """Test training with new indicators."""
    print("🧪 TESTING NEW TRAINING WITH UPDATED INDICATORS")
    print("=" * 60)
    print("📊 Using: VWAP, RSI(5), Bollinger Bands, ETH/BTC ratio")
    print("=" * 60)
    
    # Initialize training pipeline
    pipeline = IntegratedTrainingPipeline(initial_balance=300.0)
    
    print(f"\n✅ Training pipeline initialized")
    print(f"📊 Feature dimension: {pipeline.feature_extractor.get_feature_dim()}")
    print(f"🎯 Model architecture: TCN-PPO with enhanced features")
    
    # Test data collection
    print(f"\n📈 Testing data collection...")
    try:
        training_data = await pipeline.collect_training_data(days=7)  # Small test
        print(f"✅ Collected {len(training_data)} candles for testing")
        
        # Test feature extraction with new indicators
        print(f"\n🔧 Testing feature extraction...")
        features = pipeline.feature_extractor.extract_features(training_data)
        print(f"✅ Features extracted: {len(features)} dimensions")
        print(f"📊 Features per candle: {len(features) // 24}")
        
        # Test episode simulation
        print(f"\n🎮 Testing episode simulation...")
        episode_data = await pipeline.simulate_episode(training_data)
        print(f"✅ Episode simulated successfully")
        print(f"📊 Final balance: ${episode_data['final_balance']:.2f}")
        print(f"📊 Total return: {episode_data['total_return']:+.2f}%")
        
        # Quick training test (just 2 episodes)
        print(f"\n🚀 Testing quick training (2 episodes)...")
        history = await pipeline.train_model(episodes=2)
        print(f"✅ Training test completed!")
        print(f"📊 Episodes trained: {len(history)}")
        
        if history:
            best_episode = max(history, key=lambda x: x['total_reward'])
            print(f"🏆 Best episode performance:")
            print(f"   Balance: ${best_episode['final_balance']:.2f}")
            print(f"   Return: {best_episode['total_return']:+.2f}%")
        
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"✅ New indicators working correctly in training")
        print(f"✅ Ready for full 1000-cycle evaluation")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main test function."""
    success = await test_new_training()
    
    if success:
        print(f"\n🚀 READY FOR PRODUCTION TRAINING!")
        print(f"📊 New indicators validated and working")
        print(f"🎯 Proceed with full evaluation")
    else:
        print(f"\n❌ Issues found - need to fix before proceeding")


if __name__ == "__main__":
    asyncio.run(main())
