"""
Train and evaluate CustomTCNPPO agent using the most recent 90 days of data from the SQLite database.
- 60 days in-sample training
- 30 days out-of-sample testing
- Detailed HTML report with equity curve, drawdown, and PnL
"""
import os
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
import sys, os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))
from src.env.trading_env import TradingEnv
from src.agent.custom_tcn_ppo import CustomTCNPPO
from src.data.data_handler import DataHandler
from collections import deque

class SeqObsWrapper:
    def __init__(self, env, seq_len=16):
        self.env = env
        self.seq_len = seq_len
        self.reset()
    def reset(self):
        obs = self.env.reset()
        self.obs_queue = deque([obs]*self.seq_len, maxlen=self.seq_len)
        return np.stack(self.obs_queue)
    def step(self, action):
        obs, reward, done, info = self.env.step(action)
        self.obs_queue.append(obs)
        return np.stack(self.obs_queue), reward, done, info
    @property
    def action_space(self):
        return self.env.action_space
    @property
    def obs_dim(self):
        return self.env.observation_space.shape[0]

if __name__ == '__main__':
    # --- CONFIG ---
    API_KEY = os.getenv('BINANCE_API_KEY', '')
    API_SECRET = os.getenv('BINANCE_API_SECRET', '')
    MARKET = 'BTC/USDT'
    INTERVAL = '1h'
    DATA_PATH = os.path.abspath(os.path.join(os.path.dirname(__file__), '../data'))
    DB_PATH = os.path.join(DATA_PATH, 'market_data.db')
    SEQ_LEN = 16
    N_EPISODES = 30  # you may increase for more robust training
    MAX_STEPS = 1000

    # --- DATA ---
    handler = DataHandler(API_KEY, API_SECRET, MARKET, INTERVAL, DATA_PATH, db_path=DB_PATH)
    handler.fetch_latest_n_days(90)
    # Get the most recent 90 days as DataFrame
    end_date = datetime.utcnow().strftime('%Y-%m-%d')
    start_date = (datetime.utcnow() - timedelta(days=90)).strftime('%Y-%m-%d')
    df = handler.fetch_historical_data(start_date, end_date)
    train_df, test_df = handler.split_train_test_oot(df)
    # ETHBTC ratio for env
    ethbtc_df = handler.get_intermarket('ETH/BTC', INTERVAL, start_date, end_date)
    ethbtc_df = ethbtc_df.reindex(df.index, method='ffill')
    train_ethbtc, test_ethbtc = ethbtc_df.iloc[:len(train_df)], ethbtc_df.iloc[len(train_df):]

    # --- ENV/AGENT ---
    train_env = SeqObsWrapper(TradingEnv(train_df, train_ethbtc), seq_len=SEQ_LEN)
    test_env = SeqObsWrapper(TradingEnv(test_df, test_ethbtc), seq_len=SEQ_LEN)
    # Set higher entropy regularization for more exploration
    agent = CustomTCNPPO(obs_dim=train_env.obs_dim, seq_len=SEQ_LEN, n_actions=train_env.action_space.n, ent_coef=0.05)

    # --- TRAIN ---
    import pathlib
    best_composite = -np.inf
    best_agent = None
    model_save_path = None
    for ep in range(N_EPISODES):
        obs_seq = train_env.reset()
        done = False
        episode_rewards = []
        epsilon = max(0.2, 1 - ep / N_EPISODES)  # Decays from 1 to 0.2
        forced_sell_opened = False
        forced_sell_closed = False
        step_count = 0
        # Debug: Print episode start
        print(f"[DEBUG][EPISODE {ep+1}] Starting episode.")
        while not done:
            # For the first episode, force a sell (action=1) and then hold (action=2) until the short closes
            if ep == 0 and not forced_sell_opened:
                action = 1  # Force 'sell' action
                log_prob = 0.0
                value = 0.0
                forced_sell_opened = True
                print(f"[DEBUG][EPISODE {ep+1}][STEP {step_count}] Forced SELL action (open short).")
            elif ep == 0 and forced_sell_opened and not forced_sell_closed:
                # Keep forcing 'hold' until position is closed
                # Check if position is flat after step (use info or env)
                if hasattr(train_env.env, 'position') and train_env.env.position == 0:
                    forced_sell_closed = True
                    print(f"[DEBUG][EPISODE {ep+1}][STEP {step_count}] Short position closed. Reverting to normal exploration.")
                    # After closing, continue with normal exploration
                    if np.random.rand() < epsilon:
                        action = np.random.choice([0, 1, 2])
                        log_prob = 0.0
                        value = 0.0
                    else:
                        action, log_prob, value = agent.act(obs_seq)
                else:
                    action = 2  # Hold
                    log_prob = 0.0
                    value = 0.0
                    print(f"[DEBUG][EPISODE {ep+1}][STEP {step_count}] Forced HOLD action (maintain short). Position: {getattr(train_env.env, 'position', 'N/A')}")
            else:
                if np.random.rand() < epsilon:
                    action = np.random.choice([0, 1, 2])
                    log_prob = 0.0
                    value = 0.0
                else:
                    action, log_prob, value = agent.act(obs_seq)
            print(f"[DEBUG][EPISODE {ep+1}][STEP {step_count}] Action: {action}, Position: {getattr(train_env.env, 'position', 'N/A')}")
            next_obs_seq, reward, done, info = train_env.step(action)
            episode_rewards.append(reward)
            obs_seq = next_obs_seq
            step_count += 1
        # (Above: first episode forces a sell and holds until closed, then normal exploration. Debug prints show action/position.)

        # (Above: epsilon-greedy and higher entropy regularization encourage diverse exploration)

        composite = info.get('composite_reward', 0)
        if composite > best_composite:
            best_composite = composite
            best_agent = agent
            # Save best model if possible
            if hasattr(best_agent, 'save'):
                pathlib.Path('saved_models').mkdir(exist_ok=True)
                from datetime import datetime
                dt_str = datetime.now().strftime('%Y%m%d_%H%M%S')
                model_save_path = f'saved_models/tcn_ppo_best_{dt_str}.zip'
                best_agent.save(model_save_path)
        print(f"Episode {ep+1}: Total Reward: {sum(episode_rewards):.2f}, Composite: {composite*100:.2f}%")
    if model_save_path:
        print(f'Best model saved to: {model_save_path}')

    # --- TEST/EVALUATION ---
    obs_seq = test_env.reset()
    done = False
    equity_curve = []
    drawdown_curve = []
    trade_log = []
    while not done:
        action, log_prob, value = best_agent.act(obs_seq)
        obs_seq, reward, done, info = test_env.step(action)
        equity_curve.append(test_env.env.balance)
        # Drawdown
        peak = max(equity_curve)
        dd = (test_env.env.balance - peak) / peak if peak > 0 else 0
        drawdown_curve.append(dd)
        # Log trades with risk info
        if 'trade' in info:
            trade_log.append({
                'step': test_env.env.current_step,
                'action': info['trade'],
                'entry_price': getattr(test_env.env, 'entry_price', None),
                'exit_price': getattr(test_env.env, 'close', None),
                'pnl': reward,
                'balance': test_env.env.balance,
                'size_at_entry': info.get('size_at_entry'),
                'stop_loss': info.get('stop_loss'),
                'balance_at_entry': info.get('balance_at_entry')
            })
    # --- REPORT ---
    import pathlib
    from datetime import datetime
    results_dir = pathlib.Path(r'C:/Users/<USER>/Documents/Trading system/Trading project VPS/training_headless/src/results')
    results_dir.mkdir(exist_ok=True)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_path = results_dir / f'tcn_ppo_trade_report_{timestamp}.html'
    html = '<html><head><title>TCN PPO Trade Report</title><style>table{border-collapse:collapse;}th,td{border:1px solid #ccc;padding:4px;}th{background:#eee;}</style></head><body>'
    html += f'<h2>TCN PPO Out-of-Sample Test Report</h2>'
    html += f'<h3>Total Reward: {sum([t["pnl"] for t in trade_log]):.2f}</h3>'
    # Equity curve
    html += '<h4>Equity Curve</h4><svg width="800" height="200">'
    max_eq = max(equity_curve) if equity_curve else 1
    for i in range(1, len(equity_curve)):
        x1, y1 = (i-1)*800//len(equity_curve), 200-int(180*equity_curve[i-1]/max_eq)
        x2, y2 = i*800//len(equity_curve), 200-int(180*equity_curve[i]/max_eq)
        html += f'<line x1="{x1}" y1="{y1}" x2="{x2}" y2="{y2}" stroke="green" stroke-width="2"/>'
    html += '</svg>'
    # Drawdown curve
    html += '<h4>Drawdown Curve</h4><svg width="800" height="200">'
    for i in range(1, len(drawdown_curve)):
        x1, y1 = (i-1)*800//len(drawdown_curve), 200-int(180*(drawdown_curve[i-1]+1)/2)
        x2, y2 = i*800//len(drawdown_curve), 200-int(180*(drawdown_curve[i]+1)/2)
        html += f'<line x1="{x1}" y1="{y1}" x2="{x2}" y2="{y2}" stroke="red" stroke-width="2"/>'
    html += '</svg>'
    # Trade log
    html += '<h4>Trade Log (first 20 shown)</h4><table><tr><th>Step</th><th>Action</th><th>Entry</th><th>Exit</th><th>PnL</th><th>Balance</th><th>Size</th><th>Stop Loss</th><th>Risk %</th></tr>'
    for t in trade_log[:20]:
        risk_amt = None
        risk_pct = None
        if t['entry_price'] is not None and t['size_at_entry'] is not None and t['stop_loss'] is not None and t['balance_at_entry'] is not None:
            risk_amt = abs(t['entry_price'] - t['stop_loss']) * t['size_at_entry']
            risk_pct = risk_amt / t['balance_at_entry'] if t['balance_at_entry'] else None
        html += f"<tr><td>{t['step']}</td><td>{t['action']}</td><td>{t['entry_price']}</td><td>{t['exit_price']}</td><td>{t['pnl']}</td><td>{t['balance']}</td><td>{t['size_at_entry']}</td><td>{t['stop_loss']}</td><td>{risk_pct*100:.2f}%</td></tr>" if risk_pct is not None else f"<tr><td>{t['step']}</td><td>{t['action']}</td><td>{t['entry_price']}</td><td>{t['exit_price']}</td><td>{t['pnl']}</td><td>{t['balance']}</td><td>{t['size_at_entry']}</td><td>{t['stop_loss']}</td><td>N/A</td></tr>"
