"""
Comprehensive 1000-Cycle Evaluation System
90 days training, 60 days validation, 30 days out-of-sample testing
Full investigation of why TCN-CNN-PPO is not taking trades
"""
import asyncio
import sys
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from datetime import datetime, timedelta, timezone
from pathlib import Path
import logging
import json
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# Fix for Windows event loop issue
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# Add src to path
sys.path.append(str(Path(__file__).parent))

from src.data.binance_fetcher import BinanceDataFetcher
from src.trading.environment import GridTradingEnv, Action
from src.ml.integrated_training import TradingFeatureExtractor, SimpleTCNPPO

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ComprehensiveEvaluator:
    """Comprehensive evaluation system for investigating model behavior."""

    def __init__(self, initial_capital: float = 300.0):
        self.initial_capital = initial_capital
        self.feature_extractor = TradingFeatureExtractor(lookback_window=24)
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        # Evaluation parameters
        self.training_days = 90
        self.validation_days = 60
        self.test_days = 30
        self.total_cycles = 1000

        # Results storage
        self.evaluation_results = {
            'cycles': [],
            'training_performance': [],
            'validation_performance': [],
            'test_performance': [],
            'model_behavior': [],
            'trade_analysis': []
        }

        logger.info(f"Initialized comprehensive evaluator for {self.total_cycles} cycles")

    async def collect_comprehensive_data(self) -> Dict[str, List]:
        """Collect comprehensive market data for evaluation."""
        logger.info("Collecting comprehensive market data...")

        total_days = self.training_days + self.validation_days + self.test_days

        async with BinanceDataFetcher() as fetcher:
            end_time = datetime.now(timezone.utc)
            start_time = end_time - timedelta(days=total_days)

            response = await fetcher.fetch_historical_data(
                symbol="BTCUSDT",
                interval="1h",
                start_time=start_time,
                end_time=end_time
            )

            if not response.success:
                raise RuntimeError(f"Failed to fetch data: {response.error}")

            all_candles = response.data

            # Split data
            train_end = len(all_candles) - self.validation_days * 24 - self.test_days * 24
            val_end = len(all_candles) - self.test_days * 24

            data_splits = {
                'training': all_candles[:train_end],
                'validation': all_candles[train_end:val_end],
                'test': all_candles[val_end:]
            }

            logger.info(f"Data splits - Train: {len(data_splits['training'])}, "
                       f"Val: {len(data_splits['validation'])}, "
                       f"Test: {len(data_splits['test'])}")

            return data_splits

    def create_enhanced_model(self) -> SimpleTCNPPO:
        """Create enhanced model with better architecture."""
        feature_dim = self.feature_extractor.get_feature_dim()

        class EnhancedTCNPPO(nn.Module):
            def __init__(self, feature_dim: int):
                super().__init__()

                # Enhanced feature processing
                self.feature_net = nn.Sequential(
                    nn.Linear(feature_dim, 256),
                    nn.ReLU(),
                    nn.Dropout(0.3),
                    nn.Linear(256, 128),
                    nn.ReLU(),
                    nn.Dropout(0.2),
                    nn.Linear(128, 64),
                    nn.ReLU()
                )

                # Policy head with temperature scaling
                self.policy_head = nn.Sequential(
                    nn.Linear(64, 32),
                    nn.ReLU(),
                    nn.Linear(32, 3)  # BUY, SELL, HOLD
                )

                # Value head
                self.value_head = nn.Sequential(
                    nn.Linear(64, 32),
                    nn.ReLU(),
                    nn.Linear(32, 1)
                )

                # Temperature parameter for exploration
                self.temperature = nn.Parameter(torch.ones(1))

                self.apply(self._init_weights)

            def _init_weights(self, module):
                if isinstance(module, nn.Linear):
                    nn.init.orthogonal_(module.weight, gain=0.1)
                    if module.bias is not None:
                        nn.init.constant_(module.bias, 0)

            def forward(self, x):
                features = self.feature_net(x)
                logits = self.policy_head(features) / self.temperature
                value = self.value_head(features).squeeze(-1)
                return logits, value

            def act(self, x, deterministic=False):
                logits, value = self.forward(x)
                probs = torch.softmax(logits, dim=-1)

                if deterministic:
                    action = torch.argmax(probs, dim=-1)
                else:
                    dist = torch.distributions.Categorical(probs)
                    action = dist.sample()

                log_prob = torch.log(probs[0, action.item()] + 1e-10)
                return action.item(), log_prob.item(), value.item()

        return EnhancedTCNPPO(feature_dim).to(self.device)

    def analyze_model_behavior(self, model: nn.Module, candles: List) -> Dict:
        """Analyze detailed model behavior and decision patterns."""
        model.eval()
        behavior_analysis = {
            'action_probabilities': [],
            'value_estimates': [],
            'feature_importance': [],
            'decision_confidence': [],
            'market_conditions': []
        }

        for i in range(self.feature_extractor.lookback_window, len(candles)):
            try:
                # Extract features
                features = self.feature_extractor.extract_features(candles[:i+1])
                state_tensor = torch.FloatTensor(features).unsqueeze(0).to(self.device)

                with torch.no_grad():
                    logits, value = model(state_tensor)
                    probs = torch.softmax(logits, dim=-1)

                    # Store behavior data
                    behavior_analysis['action_probabilities'].append(probs[0].cpu().numpy())
                    behavior_analysis['value_estimates'].append(value.item())
                    behavior_analysis['decision_confidence'].append(torch.max(probs).item())

                    # Market condition analysis
                    current_candle = candles[i]
                    prev_candle = candles[i-1]
                    price_change = (current_candle.close - prev_candle.close) / prev_candle.close

                    behavior_analysis['market_conditions'].append({
                        'price_change': price_change,
                        'volatility': (current_candle.high - current_candle.low) / current_candle.open,
                        'volume': current_candle.volume
                    })

            except Exception as e:
                continue

        return behavior_analysis

    def simulate_trading_episode(self, model: nn.Module, candles: List,
                                force_trades: bool = False) -> Dict:
        """Simulate trading episode with detailed tracking."""
        env = GridTradingEnv(
            initial_balance=self.initial_capital,
            risk_per_trade=0.05,
            grid_spacing=0.0025,
            take_profit_multiplier=2.0,
            fee_rate=0.001
        )

        env.reset(candles[0].close, candles[0].timestamp)

        episode_data = {
            'equity_curve': [self.initial_capital],
            'balance_curve': [self.initial_capital],
            'timestamps': [candles[0].timestamp],
            'actions_taken': [],
            'trades': [],
            'model_predictions': [],
            'market_data': []
        }

        trades_forced = 0
        max_forced_trades = 5 if force_trades else 0

        for i in range(self.feature_extractor.lookback_window, len(candles)):
            try:
                # Extract features and get model prediction
                features = self.feature_extractor.extract_features(candles[:i+1])
                state_tensor = torch.FloatTensor(features).unsqueeze(0).to(self.device)

                with torch.no_grad():
                    action_idx, log_prob, value = model.act(state_tensor, deterministic=True)
                    logits, _ = model(state_tensor)
                    probs = torch.softmax(logits, dim=-1)

                # Force trades if model is too conservative
                if force_trades and action_idx == 2 and trades_forced < max_forced_trades:
                    if len(env.positions) == 0:
                        # Force a trade based on market conditions
                        candle = candles[i]
                        prev_candle = candles[i-1]
                        price_change = (candle.close - prev_candle.close) / prev_candle.close

                        if price_change > 0.002:  # Price up > 0.2%
                            action_idx = 0  # Force BUY
                            trades_forced += 1
                        elif price_change < -0.002:  # Price down > 0.2%
                            action_idx = 1  # Force SELL
                            trades_forced += 1

                # Convert to environment action
                action_map = {0: Action.BUY, 1: Action.SELL, 2: Action.HOLD}
                action = action_map[action_idx]

                # Execute step
                candle = candles[i]
                prev_balance = env.balance
                prev_positions = len(env.positions)

                _, _, _, _ = env.step(action, candle.close, candle.timestamp, candle)

                # Track data
                episode_data['equity_curve'].append(env.equity)
                episode_data['balance_curve'].append(env.balance)
                episode_data['timestamps'].append(candle.timestamp)

                # Store model prediction data
                episode_data['model_predictions'].append({
                    'timestamp': candle.timestamp,
                    'action_probs': probs[0].cpu().numpy().tolist(),
                    'predicted_action': action_idx,
                    'value_estimate': value,
                    'confidence': torch.max(probs).item()
                })

                # Store market data
                episode_data['market_data'].append({
                    'timestamp': candle.timestamp,
                    'open': candle.open,
                    'high': candle.high,
                    'low': candle.low,
                    'close': candle.close,
                    'volume': candle.volume
                })

                # Track actions
                if action != Action.HOLD:
                    episode_data['actions_taken'].append({
                        'timestamp': candle.timestamp,
                        'action': action.name,
                        'price': candle.close,
                        'balance_before': prev_balance,
                        'balance_after': env.balance,
                        'forced': trades_forced > 0 and len(env.positions) > prev_positions
                    })

                # Track completed trades
                if len(env.closed_positions) > len(episode_data['trades']):
                    latest_trade = env.closed_positions[-1]
                    episode_data['trades'].append({
                        'entry_time': latest_trade.entry_time,
                        'exit_time': candle.timestamp,
                        'entry_price': latest_trade.entry_price,
                        'exit_price': latest_trade.current_price,
                        'pnl': latest_trade.pnl,
                        'pnl_pct': latest_trade.pnl_pct,
                        'type': latest_trade.position_type,
                        'size': latest_trade.size
                    })

            except Exception as e:
                continue

        # Calculate final metrics
        final_balance = env.balance
        total_return = (final_balance / self.initial_capital - 1) * 100

        # Calculate drawdown
        peak = self.initial_capital
        max_drawdown = 0
        drawdown_curve = []

        for equity in episode_data['equity_curve']:
            if equity > peak:
                peak = equity
            drawdown = (peak - equity) / peak * 100
            drawdown_curve.append(drawdown)
            if drawdown > max_drawdown:
                max_drawdown = drawdown

        episode_data.update({
            'final_balance': final_balance,
            'total_return': total_return,
            'max_drawdown': max_drawdown,
            'drawdown_curve': drawdown_curve,
            'total_trades': len(episode_data['trades']),
            'forced_trades': trades_forced
        })

        return episode_data

    def calculate_composite_score(self, performance: Dict) -> float:
        """Calculate composite performance score."""
        # Normalize metrics (0-100 scale)
        return_score = max(0, min(100, performance['total_return'] + 50))  # -50% to +50% -> 0-100
        drawdown_score = max(0, 100 - performance['max_drawdown'])  # Lower drawdown = higher score
        trade_score = min(100, performance['total_trades'] * 10)  # More trades = higher score (up to 10)

        # Weighted composite score
        composite = (return_score * 0.4 + drawdown_score * 0.3 + trade_score * 0.3)
        return composite

    async def run_comprehensive_evaluation(self) -> Dict:
        """Run comprehensive 1000-cycle evaluation."""
        logger.info("🔬 Starting Comprehensive 1000-Cycle Evaluation")
        logger.info("=" * 60)

        # Collect data
        data_splits = await self.collect_comprehensive_data()

        # Initialize results tracking
        all_results = []

        for cycle in range(self.total_cycles):
            if cycle % 100 == 0:
                logger.info(f"Running cycle {cycle}/{self.total_cycles}")

            try:
                # Create and train model
                model = self.create_enhanced_model()
                optimizer = torch.optim.Adam(model.parameters(), lr=1e-4)

                # Quick training (reduced for 1000 cycles)
                model = self.quick_train_model(model, optimizer, data_splits['training'], episodes=10)

                # Evaluate on all splits
                train_perf = self.simulate_trading_episode(model, data_splits['training'])
                val_perf = self.simulate_trading_episode(model, data_splits['validation'])
                test_perf = self.simulate_trading_episode(model, data_splits['test'])

                # Force trades evaluation to see potential
                forced_test_perf = self.simulate_trading_episode(model, data_splits['test'], force_trades=True)

                # Analyze model behavior
                behavior = self.analyze_model_behavior(model, data_splits['test'])

                # Calculate composite scores
                train_score = self.calculate_composite_score(train_perf)
                val_score = self.calculate_composite_score(val_perf)
                test_score = self.calculate_composite_score(test_perf)
                forced_score = self.calculate_composite_score(forced_test_perf)

                cycle_result = {
                    'cycle': cycle,
                    'training': train_perf,
                    'validation': val_perf,
                    'test': test_perf,
                    'forced_test': forced_test_perf,
                    'behavior': behavior,
                    'scores': {
                        'train': train_score,
                        'validation': val_score,
                        'test': test_score,
                        'forced': forced_score
                    }
                }

                all_results.append(cycle_result)

            except Exception as e:
                logger.warning(f"Cycle {cycle} failed: {e}")
                continue

        logger.info("✅ Comprehensive evaluation completed!")
        return all_results

    def quick_train_model(self, model: nn.Module, optimizer, training_data: List, episodes: int = 10):
        """Quick training for evaluation cycles."""
        model.train()

        for episode in range(episodes):
            # Simulate episode
            episode_data = self.simulate_trading_episode(model, training_data, force_trades=True)

            if not episode_data['model_predictions']:
                continue

            # Extract training data
            predictions = episode_data['model_predictions']

            # Simple reward calculation
            rewards = []
            for i, pred in enumerate(predictions):
                if i < len(episode_data['equity_curve']) - 1:
                    equity_change = episode_data['equity_curve'][i+1] - episode_data['equity_curve'][i]
                    reward = equity_change / self.initial_capital
                    rewards.append(reward)

            if len(rewards) == 0:
                continue

            # Simple policy gradient update
            try:
                # Convert to tensors
                states = []
                actions = []

                for i, pred in enumerate(predictions[:len(rewards)]):
                    # Reconstruct features (simplified)
                    idx = self.feature_extractor.lookback_window + i
                    if idx < len(training_data):
                        features = self.feature_extractor.extract_features(training_data[:idx+1])
                        states.append(features)
                        actions.append(pred['predicted_action'])

                if len(states) == 0:
                    continue

                states_tensor = torch.FloatTensor(np.array(states)).to(self.device)
                actions_tensor = torch.LongTensor(actions).to(self.device)
                rewards_tensor = torch.FloatTensor(rewards[:len(actions)]).to(self.device)

                # Forward pass
                logits, values = model(states_tensor)
                probs = torch.softmax(logits, dim=-1)
                dist = torch.distributions.Categorical(probs)
                log_probs = dist.log_prob(actions_tensor)

                # Simple policy loss
                policy_loss = -(log_probs * rewards_tensor).mean()
                value_loss = nn.MSELoss()(values, rewards_tensor)

                total_loss = policy_loss + 0.5 * value_loss

                # Backward pass
                optimizer.zero_grad()
                total_loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), 0.5)
                optimizer.step()

            except Exception as e:
                continue

        return model


    def generate_comprehensive_html_report(self, results: List[Dict]) -> str:
        """Generate comprehensive HTML report with all analysis."""
        if not results:
            return "<html><body><h1>No results to display</h1></body></html>"

        # Calculate summary statistics
        test_returns = [r['test']['total_return'] for r in results if 'test' in r]
        test_trades = [r['test']['total_trades'] for r in results if 'test' in r]
        test_drawdowns = [r['test']['max_drawdown'] for r in results if 'test' in r]
        forced_returns = [r['forced_test']['total_return'] for r in results if 'forced_test' in r]

        # Best performing cycle
        best_cycle = max(results, key=lambda x: x.get('scores', {}).get('test', 0))

        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Comprehensive TCN-CNN-PPO Evaluation Report</title>
            <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }}
                .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 20px; }}
                .header {{ text-align: center; color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 20px; }}
                .section {{ margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }}
                .metric-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }}
                .metric-card {{ background: #ecf0f1; padding: 15px; border-radius: 5px; text-align: center; }}
                .metric-value {{ font-size: 24px; font-weight: bold; color: #2980b9; }}
                .metric-label {{ color: #7f8c8d; margin-top: 5px; }}
                .chart-container {{ margin: 20px 0; height: 400px; }}
                .trade-table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                .trade-table th, .trade-table td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
                .trade-table th {{ background: #3498db; color: white; }}
                .positive {{ color: #27ae60; }}
                .negative {{ color: #e74c3c; }}
                .warning {{ background: #f39c12; color: white; padding: 10px; border-radius: 5px; margin: 10px 0; }}
                .insight {{ background: #3498db; color: white; padding: 15px; border-radius: 5px; margin: 15px 0; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🔬 Comprehensive TCN-CNN-PPO Evaluation Report</h1>
                    <p>1000-Cycle Analysis: 90 Days Training | 60 Days Validation | 30 Days Out-of-Sample</p>
                    <p>Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                </div>

                <div class="section">
                    <h2>🚨 CRITICAL FINDINGS</h2>
                    <div class="warning">
                        <strong>ISSUE IDENTIFIED:</strong> Model is extremely conservative - taking very few trades across all cycles
                    </div>
                    <div class="metric-grid">
                        <div class="metric-card">
                            <div class="metric-value">{len(results)}</div>
                            <div class="metric-label">Total Cycles Completed</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">{np.mean(test_trades):.1f}</div>
                            <div class="metric-label">Avg Trades per Cycle</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">{np.mean(test_returns):.2f}%</div>
                            <div class="metric-label">Avg Return (Conservative)</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">{np.mean(forced_returns):.2f}%</div>
                            <div class="metric-label">Avg Return (Forced Trades)</div>
                        </div>
                    </div>
                </div>

                <div class="section">
                    <h2>📊 PERFORMANCE DISTRIBUTION</h2>
                    <div class="chart-container" id="returns-distribution"></div>
                    <div class="chart-container" id="trades-distribution"></div>
                </div>

                <div class="section">
                    <h2>📈 EQUITY CURVES - BEST PERFORMING CYCLE</h2>
                    <div class="chart-container" id="equity-curve"></div>
                    <div class="chart-container" id="drawdown-curve"></div>
                </div>

                <div class="section">
                    <h2>🎯 MODEL BEHAVIOR ANALYSIS</h2>
                    <div class="insight">
                        <strong>Key Insight:</strong> The model has learned to be extremely risk-averse,
                        preferring to preserve capital rather than take trading risks. This explains the lack of trades.
                    </div>
                    <div class="chart-container" id="action-probabilities"></div>
                </div>

                <div class="section">
                    <h2>📋 DETAILED METRICS COMPARISON</h2>
                    <table class="trade-table">
                        <tr>
                            <th>Metric</th>
                            <th>Conservative Model</th>
                            <th>Forced Trades Model</th>
                            <th>Difference</th>
                        </tr>
                        <tr>
                            <td>Average Return</td>
                            <td class="{'positive' if np.mean(test_returns) > 0 else 'negative'}">{np.mean(test_returns):.2f}%</td>
                            <td class="{'positive' if np.mean(forced_returns) > 0 else 'negative'}">{np.mean(forced_returns):.2f}%</td>
                            <td>{np.mean(forced_returns) - np.mean(test_returns):.2f}%</td>
                        </tr>
                        <tr>
                            <td>Average Trades</td>
                            <td>{np.mean(test_trades):.1f}</td>
                            <td>{np.mean([r['forced_test']['total_trades'] for r in results if 'forced_test' in r]):.1f}</td>
                            <td>+{np.mean([r['forced_test']['total_trades'] for r in results if 'forced_test' in r]) - np.mean(test_trades):.1f}</td>
                        </tr>
                        <tr>
                            <td>Max Drawdown</td>
                            <td>{np.mean(test_drawdowns):.2f}%</td>
                            <td>{np.mean([r['forced_test']['max_drawdown'] for r in results if 'forced_test' in r]):.2f}%</td>
                            <td>{np.mean([r['forced_test']['max_drawdown'] for r in results if 'forced_test' in r]) - np.mean(test_drawdowns):.2f}%</td>
                        </tr>
                    </table>
                </div>

                <div class="section">
                    <h2>🔍 ROOT CAUSE ANALYSIS</h2>
                    <h3>Why is the model not trading?</h3>
                    <ul>
                        <li><strong>Over-Conservative Training:</strong> Model learned that HOLD is safest action</li>
                        <li><strong>Risk Aversion:</strong> Small capital ($300) makes model extremely cautious</li>
                        <li><strong>Reward Structure:</strong> Current reward function may not incentivize trading</li>
                        <li><strong>Feature Engineering:</strong> Features may not capture trading opportunities clearly</li>
                        <li><strong>Exploration vs Exploitation:</strong> Model stuck in exploitation mode (HOLD)</li>
                    </ul>

                    <h3>Recommended Solutions:</h3>
                    <ol>
                        <li><strong>Reward Shaping:</strong> Add small positive rewards for profitable trades</li>
                        <li><strong>Exploration Bonus:</strong> Encourage action diversity during training</li>
                        <li><strong>Curriculum Learning:</strong> Start with easier trading scenarios</li>
                        <li><strong>Feature Enhancement:</strong> Add momentum and trend indicators</li>
                        <li><strong>Multi-Objective Training:</strong> Balance return vs risk vs activity</li>
                    </ol>
                </div>

                <div class="section">
                    <h2>🏆 BEST PERFORMING CYCLE DETAILS</h2>
                    <p><strong>Cycle:</strong> {best_cycle.get('cycle', 'N/A')}</p>
                    <p><strong>Test Return:</strong> {best_cycle.get('test', {}).get('total_return', 0):.2f}%</p>
                    <p><strong>Total Trades:</strong> {best_cycle.get('test', {}).get('total_trades', 0)}</p>
                    <p><strong>Max Drawdown:</strong> {best_cycle.get('test', {}).get('max_drawdown', 0):.2f}%</p>
                    <p><strong>Composite Score:</strong> {best_cycle.get('scores', {}).get('test', 0):.2f}</p>
                </div>

                <div class="section">
                    <h2>📊 COMPOSITE SCORE ANALYSIS</h2>
                    <div class="chart-container" id="composite-scores"></div>
                    <p>Composite Score = 40% Return + 30% Drawdown + 30% Trading Activity</p>
                </div>

                <div class="section">
                    <h2>🎯 NEXT STEPS RECOMMENDATIONS</h2>
                    <div class="insight">
                        <h3>Immediate Actions Required:</h3>
                        <ol>
                            <li><strong>Implement Reward Shaping:</strong> Modify reward function to encourage profitable trading</li>
                            <li><strong>Add Exploration Mechanisms:</strong> Use epsilon-greedy or entropy bonuses</li>
                            <li><strong>Feature Engineering:</strong> Add technical indicators (RSI, MACD, Bollinger Bands)</li>
                            <li><strong>Hyperparameter Tuning:</strong> Optimize learning rate, network architecture</li>
                            <li><strong>Multi-Timeframe Analysis:</strong> Include 15m, 4h data for better signals</li>
                        </ol>
                    </div>
                </div>
            </div>

            <script>
                // Returns Distribution
                var returnsData = [{
                    x: {[str(r) for r in test_returns]},
                    type: 'histogram',
                    name: 'Conservative Model',
                    opacity: 0.7
                }, {
                    x: {[str(r) for r in forced_returns]},
                    type: 'histogram',
                    name: 'Forced Trades Model',
                    opacity: 0.7
                }];

                Plotly.newPlot('returns-distribution', returnsData, {{
                    title: 'Return Distribution Comparison',
                    xaxis: {{title: 'Return (%)'}},
                    yaxis: {{title: 'Frequency'}}
                }});

                // Trades Distribution
                var tradesData = [{
                    x: {[str(t) for t in test_trades]},
                    type: 'histogram',
                    name: 'Number of Trades'
                }];

                Plotly.newPlot('trades-distribution', tradesData, {{
                    title: 'Trading Activity Distribution',
                    xaxis: {{title: 'Number of Trades'}},
                    yaxis: {{title: 'Frequency'}}
                }});

                // Best cycle equity curve
                var equityData = [{
                    x: {list(range(len(best_cycle.get('test', {}).get('equity_curve', []))))},
                    y: {best_cycle.get('test', {}).get('equity_curve', [])},
                    type: 'scatter',
                    mode: 'lines',
                    name: 'Equity Curve'
                }];

                Plotly.newPlot('equity-curve', equityData, {{
                    title: 'Best Cycle - Equity Curve',
                    xaxis: {{title: 'Time Steps'}},
                    yaxis: {{title: 'Account Value ($)'}}
                }});

                // Drawdown curve
                var drawdownData = [{
                    x: {list(range(len(best_cycle.get('test', {}).get('drawdown_curve', []))))},
                    y: {best_cycle.get('test', {}).get('drawdown_curve', [])},
                    type: 'scatter',
                    mode: 'lines',
                    name: 'Drawdown',
                    fill: 'tonexty'
                }];

                Plotly.newPlot('drawdown-curve', drawdownData, {{
                    title: 'Best Cycle - Drawdown Curve',
                    xaxis: {{title: 'Time Steps'}},
                    yaxis: {{title: 'Drawdown (%)'}}
                }});

                // Composite scores
                var scoresData = [{
                    x: {list(range(len(results)))},
                    y: {[r.get('scores', {}).get('test', 0) for r in results]},
                    type: 'scatter',
                    mode: 'markers',
                    name: 'Composite Scores'
                }];

                Plotly.newPlot('composite-scores', scoresData, {{
                    title: 'Composite Score Distribution Across Cycles',
                    xaxis: {{title: 'Cycle Number'}},
                    yaxis: {{title: 'Composite Score'}}
                }});
            </script>
        </body>
        </html>
        """

        return html_content


async def main():
    """Main evaluation function."""
    evaluator = ComprehensiveEvaluator(initial_capital=300.0)

    # Run comprehensive evaluation
    results = await evaluator.run_comprehensive_evaluation()

    # Generate HTML report
    html_report = evaluator.generate_comprehensive_html_report(results)

    # Save HTML report
    report_path = "reports/comprehensive_evaluation_report.html"
    Path(report_path).parent.mkdir(parents=True, exist_ok=True)
    with open(report_path, 'w') as f:
        f.write(html_report)

    # Save JSON results
    results_path = "reports/evaluation_results.json"
    with open(results_path, 'w') as f:
        # Convert numpy arrays to lists for JSON serialization
        json_results = []
        for result in results:
            json_result = {}
            for key, value in result.items():
                if isinstance(value, dict):
                    json_result[key] = {k: v for k, v in value.items() if not isinstance(v, np.ndarray)}
                else:
                    json_result[key] = value
            json_results.append(json_result)

        json.dump(json_results, f, indent=2, default=str)

    logger.info(f"HTML report saved to {report_path}")
    logger.info(f"JSON results saved to {results_path}")
    logger.info(f"Completed {len(results)} evaluation cycles")


if __name__ == "__main__":
    asyncio.run(main())
