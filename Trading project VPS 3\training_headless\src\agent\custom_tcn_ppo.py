"""
Custom TCN + PPO agent for trading, implemented in PyTorch.
- Uses a Temporal Convolutional Network (TCN) as the feature extractor.
- PPO algorithm implemented from scratch for full flexibility and transparency.
- Designed for sequence-based trading environments.

Author: [Your Name]
Date: 2025-05-18
"""
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from collections import deque

class TCNBlock(nn.Module):
    """A single block of TCN with dilation and residual connection."""
    def __init__(self, in_channels, out_channels, kernel_size, dilation):
        super().__init__()
        self.conv = nn.Conv1d(in_channels, out_channels, kernel_size,
                              padding=(kernel_size-1)*dilation, dilation=dilation)
        self.relu = nn.ReLU()
        self.downsample = nn.Conv1d(in_channels, out_channels, 1) if in_channels != out_channels else nn.Identity()

    def forward(self, x):
        out = self.conv(x)
        out = self.relu(out)
        # Remove extra padding to keep output length same as input
        out = out[..., :x.shape[-1]]
        return out + self.downsample(x)

class TCN(nn.Module):
    """Temporal Convolutional Network feature extractor."""
    def __init__(self, num_inputs, num_channels, kernel_size=3):
        super().__init__()
        layers = []
        num_levels = len(num_channels)
        for i in range(num_levels):
            dilation = 2 ** i
            in_ch = num_inputs if i == 0 else num_channels[i-1]
            out_ch = num_channels[i]
            layers.append(TCNBlock(in_ch, out_ch, kernel_size, dilation))
        self.network = nn.Sequential(*layers)

    def forward(self, x):
        # x: (batch, seq_len, features) -> (batch, features, seq_len)
        x = x.transpose(1, 2)
        return self.network(x).mean(dim=2)  # Global average pooling

class ActorCritic(nn.Module):
    def __init__(self, obs_dim, seq_len, n_actions, tcn_channels=[32, 32, 32]):
        super().__init__()
        self.tcn = TCN(obs_dim, tcn_channels)
        feat_dim = tcn_channels[-1]
        self.policy = nn.Linear(feat_dim, n_actions)
        self.value = nn.Linear(feat_dim, 1)

    def forward(self, x):
        # x: (batch, seq_len, obs_dim)
        features = self.tcn(x)
        logits = self.policy(features)
        value = self.value(features)
        return logits, value.squeeze(-1)

    def act(self, x):
        logits, value = self.forward(x)
        probs = torch.softmax(logits, dim=-1)
        dist = torch.distributions.Categorical(probs)
        action = dist.sample()
        log_prob = dist.log_prob(action)
        return action, log_prob, value

    def evaluate_actions(self, x, actions):
        logits, value = self.forward(x)
        probs = torch.softmax(logits, dim=-1)
        dist = torch.distributions.Categorical(probs)
        log_prob = dist.log_prob(actions)
        entropy = dist.entropy()
        return log_prob, entropy, value

class CustomTCNPPO:
    def __init__(self, obs_dim, seq_len, n_actions, lr=3e-4, gamma=0.99, lam=0.95, clip_eps=0.2, ent_coef=0.01, vf_coef=0.5, tcn_channels=[32,32,32], device='cpu'):
        self.device = device
        self.model = ActorCritic(obs_dim, seq_len, n_actions, tcn_channels).to(device)
        self.optimizer = optim.Adam(self.model.parameters(), lr=lr)
        self.gamma = gamma
        self.lam = lam
        self.clip_eps = clip_eps
        self.ent_coef = ent_coef
        self.vf_coef = vf_coef
        self.seq_len = seq_len

    def compute_gae(self, rewards, values, dones, next_value):
        # Generalized Advantage Estimation (GAE)
        advantages = np.zeros_like(rewards)
        lastgaelam = 0
        for t in reversed(range(len(rewards))):
            nonterminal = 1.0 - dones[t]
            delta = rewards[t] + self.gamma * next_value * nonterminal - values[t]
            advantages[t] = lastgaelam = delta + self.gamma * self.lam * nonterminal * lastgaelam
            next_value = values[t]
        returns = advantages + values
        return advantages, returns

    def ppo_update(self, obs_seq, actions, log_probs_old, returns, advantages, epochs=4, batch_size=64):
        obs_seq = torch.tensor(obs_seq, dtype=torch.float32, device=self.device)
        actions = torch.tensor(actions, dtype=torch.long, device=self.device)
        log_probs_old = torch.tensor(log_probs_old, dtype=torch.float32, device=self.device)
        returns = torch.tensor(returns, dtype=torch.float32, device=self.device)
        advantages = torch.tensor(advantages, dtype=torch.float32, device=self.device)
        n = len(obs_seq)
        for _ in range(epochs):
            idx = np.random.permutation(n)
            for start in range(0, n, batch_size):
                end = start + batch_size
                mb_idx = idx[start:end]
                mb_obs = obs_seq[mb_idx]
                mb_actions = actions[mb_idx]
                mb_log_probs_old = log_probs_old[mb_idx]
                mb_returns = returns[mb_idx]
                mb_advantages = advantages[mb_idx]
                logits, values = self.model(mb_obs)
                probs = torch.softmax(logits, dim=-1)
                dist = torch.distributions.Categorical(probs)
                log_probs = dist.log_prob(mb_actions)
                entropy = dist.entropy().mean()
                ratio = torch.exp(log_probs - mb_log_probs_old)
                surr1 = ratio * mb_advantages
                surr2 = torch.clamp(ratio, 1.0 - self.clip_eps, 1.0 + self.clip_eps) * mb_advantages
                policy_loss = -torch.min(surr1, surr2).mean()
                value_loss = (mb_returns - values).pow(2).mean()
                loss = policy_loss + self.vf_coef * value_loss - self.ent_coef * entropy
                self.optimizer.zero_grad()
                loss.backward()
                self.optimizer.step()

    def act(self, obs_seq):
        obs_seq = torch.tensor(obs_seq, dtype=torch.float32, device=self.device).unsqueeze(0)
        with torch.no_grad():
            action, log_prob, value = self.model.act(obs_seq)
        return action.item(), log_prob.item(), value.item()

    def evaluate(self, obs_seq):
        obs_seq = torch.tensor(obs_seq, dtype=torch.float32, device=self.device).unsqueeze(0)
        with torch.no_grad():
            logits, value = self.model(obs_seq)
            probs = torch.softmax(logits, dim=-1)
        return probs.cpu().numpy()[0], value.cpu().numpy()[0]

