[Unit]
Description=Grid Trading System Daemon
After=network.target

[Service]
Type=simple
User=ubuntu
WorkingDirectory=/home/<USER>/trading-system
Environment="PATH=/home/<USER>/trading-system/venv/bin"
ExecStart=/home/<USER>/trading-system/venv/bin/python -m training_headless.src.daemon
Restart=always
RestartSec=10

# Security
NoNewPrivileges=true
ProtectSystem=full
PrivateTmp=true
ProtectHome=true

[Install]
WantedBy=multi-user.target
