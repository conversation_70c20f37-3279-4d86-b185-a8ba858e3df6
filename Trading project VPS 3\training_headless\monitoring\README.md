# Monitoring System for Grid Trading Bot

This directory contains the monitoring and alerting system for the Grid Trading Bot.

## Features

- **Health Monitoring**: Continuously checks system health including API connections, database status, and resource usage.
- **Alerting**: Sends notifications for critical issues via email (Slack/Telegram support coming soon).
- **Auto-Fix**: Automatically attempts to resolve common issues.
- **Logging**: Comprehensive logging of all system events and health checks.

## Components

- `health_check.py`: Core health monitoring functionality
- `alert_manager.py`: Handles alert generation and delivery
- `monitoring_service.py`: Main service that ties everything together
- `config/monitoring_config.py`: Configuration settings

## Setup

1. **Install Dependencies**:
   ```bash
   pip install -r requirements-monitoring.txt
   ```

2. **Configure Environment Variables**:
   Create a `.env` file in the project root with the following variables:
   ```
   # Monitoring
   MONITORING_INTERVAL=300
   
   # Email Alerts
   SMTP_SERVER=smtp.gmail.com
   SMTP_PORT=587
   SMTP_USERNAME=<EMAIL>
   SMTP_PASSWORD=your-app-password
   ALERT_EMAIL=<EMAIL>
   
   # Database
   DB_PATH=data/trading.db
   
   # Logging
   LOG_LEVEL=INFO
   ```

3. **Run the Monitoring Service**:
   ```bash
   python -m training_headless.scripts.run_monitoring
   ```

## Usage

### Starting the Service
```bash
# Run in foreground
python -m training_headless.scripts.run_monitoring

# Run in background (Linux/macOS)
nohup python -m training_headless.scripts.run_monitoring > monitoring.log 2>&1 &
```

### Checking Status
Check the log file for the current status:
```bash
tail -f monitoring_service.log
```

### Stopping the Service
```bash
# Find the process ID
ps aux | grep run_monitoring

# Stop the process
kill <process_id>
```

## Configuration

Edit `training_headless/config/monitoring_config.py` to modify:
- Check intervals
- Alert thresholds
- Notification settings
- Logging configuration

## Alert Types

- **Critical**: Immediate action required (e.g., API down, database errors)
- **Warning**: Attention needed but not critical (e.g., high resource usage)
- **Info**: Informational messages (e.g., service started, checks completed)

## Logs

Logs are stored in the following locations:
- `monitoring_service.log`: Main service logs
- `trading_bot.log`: Application logs

## Troubleshooting

1. **Email Not Sending**
   - Check SMTP settings in `.env`
   - Verify app password is correct for Gmail
   - Check spam folder

2. **High Resource Usage**
   - Increase check interval
   - Optimize database queries
   - Check for memory leaks

3. **Database Connection Issues**
   - Verify database path is correct
   - Check file permissions
   - Ensure database is not corrupted

## License

This project is licensed under the MIT License - see the LICENSE file for details.
