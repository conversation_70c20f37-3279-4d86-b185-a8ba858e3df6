"""Hyperparameter optimization for the trading system."""

import os
import json
import numpy as np
import optuna
from optuna.samplers import TPESampler
from optuna.pruners import MedianPruner
from typing import Dict, Any, List, Tuple, Optional
from pathlib import Path
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.tensorboard import SummaryWriter

from ..models.tcn_cnn_ppo import TCN_CNN_PPO
from ..agent.ppo_agent import PPOAgent
from ..env.trading_env import CryptoTradingEnv
from ..data.data_loader import CryptoDataLoader
from ..data.dataset import CryptoDataset
from ..data.transforms import Compose, AddIndicators, Normalize
from ..config import load_config, save_config


class HyperparameterOptimizer:
    """Hyperparameter optimization using Optuna."""
    
    def __init__(
        self,
        config: Dict[str, Any],
        data_loader: CryptoDataLoader,
        study_name: str = "trading_study",
        storage: str = "sqlite:///hpo_studies.db",
        n_trials: int = 100,
        n_jobs: int = 1,
        seed: int = 42
    ):
        """
        Initialize the hyperparameter optimizer.
        
        Args:
            config: Base configuration dictionary
            data_loader: Data loader instance
            study_name: Name of the study
            storage: Storage URL for Optuna study
            n_trials: Number of trials to run
            n_jobs: Number of parallel jobs
            seed: Random seed for reproducibility
        """
        self.config = config
        self.data_loader = data_loader
        self.study_name = study_name
        self.storage = storage
        self.n_trials = n_trials
        self.n_jobs = n_jobs
        self.seed = seed
        
        # Create output directories
        self.study_dir = Path("studies") / study_name
        self.study_dir.mkdir(parents=True, exist_ok=True)
        
        # Set device
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
    def _objective(self, trial: optuna.Trial) -> float:
        """Objective function for Optuna optimization."""
        # Sample hyperparameters
        params = self._sample_hyperparameters(trial)
        
        # Update config with sampled parameters
        trial_config = self._update_config(params)
        
        # Initialize environment and model
        env = self._create_environment(trial_config)
        model = self._create_model(trial_config)
        
        # Create trial directory
        trial_dir = self.study_dir / f"trial_{trial.number}"
        trial_dir.mkdir(exist_ok=True)
        
        # Save trial config
        save_config(trial_config, trial_dir / "config.yaml")
        
        try:
            # Train model
            metrics = self._train_trial(
                trial_config=trial_config,
                model=model,
                env=env,
                trial_dir=trial_dir,
                trial=trial
            )
            
            # Return the metric to optimize (negative for minimization)
            return -metrics["sharpe_ratio"]
            
        except Exception as e:
            print(f"Trial {trial.number} failed: {str(e)}")
            raise optuna.TrialPruned()
    
    def _sample_hyperparameters(self, trial: optuna.Trial) -> Dict[str, Any]:
        """Sample hyperparameters for a trial."""
        params = {}
        
        # Model architecture
        params["tcn_channels"] = [
            trial.suggest_int("tcn_channel_1", 16, 128, step=16),
            trial.suggest_int("tcn_channel_2", 32, 256, step=16),
            trial.suggest_int("tcn_channel_3", 64, 512, step=16)
        ]
        
        params["cnn_filters"] = [
            trial.suggest_int("cnn_filter_1", 8, 64, step=8),
            trial.suggest_int("cnn_filter_2", 16, 128, step=8)
        ]
        
        params["kernel_size"] = trial.suggest_int("kernel_size", 3, 9, step=2)
        params["dropout"] = trial.suggest_float("dropout", 0.1, 0.5, step=0.1)
        
        # Training parameters
        params["learning_rate"] = trial.suggest_float("learning_rate", 1e-5, 1e-3, log=True)
        params["gamma"] = trial.suggest_float("gamma", 0.9, 0.9999)
        params["epsilon"] = trial.suggest_float("epsilon", 0.1, 0.3)
        params["entropy_coef"] = trial.suggest_float("entropy_coef", 0.001, 0.1, log=True)
        
        # PPO parameters
        params["ppo_epochs"] = trial.suggest_int("ppo_epochs", 3, 10)
        params["batch_size"] = trial.suggest_categorical("batch_size", [32, 64, 128, 256])
        params["clip_param"] = trial.suggest_float("clip_param", 0.1, 0.3)
        
        # Environment parameters
        params["window_size"] = trial.suggest_int("window_size", 8, 32, step=4)
        params["max_position"] = trial.suggest_float("max_position", 0.1, 1.0)
        
        return params
    
    def _update_config(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Update config with sampled parameters."""
        config = self.config.copy()
        
        # Update model config
        config["model"]["tcn_channels"] = params["tcn_channels"]
        config["model"]["cnn_filters"] = params["cnn_filters"]
        config["model"]["kernel_size"] = params["kernel_size"]
        config["model"]["dropout"] = params["dropout"]
        
        # Update training config
        config["training"]["learning_rate"] = params["learning_rate"]
        config["training"]["gamma"] = params["gamma"]
        config["training"]["epsilon"] = params["epsilon"]
        config["training"]["entropy_coef"] = params["entropy_coef"]
        config["training"]["ppo_epochs"] = params["ppo_epochs"]
        config["training"]["batch_size"] = params["batch_size"]
        config["training"]["clip_param"] = params["clip_param"]
        
        # Update environment config
        config["env"]["window_size"] = params["window_size"]
        config["env"]["max_position"] = params["max_position"]
        
        return config
    
    def _create_environment(self, config: Dict[str, Any]) -> CryptoTradingEnv:
        """Create trading environment."""
        # Load and prepare data
        data = self.data_loader.load_data(
            symbol=config["data"]["symbol"],
            timeframe=config["data"]["timeframe"],
            start_date=config["data"]["train_start"],
            end_date=config["data"]["train_end"]
        )
        
        # Create transforms
        transforms = Compose([
            AddIndicators(config["data"].get("indicators", [])),
            Normalize(method="standard")
        ])
        
        # Apply transforms
        data = transforms(data.values.astype(np.float32))
        
        # Create environment
        env = CryptoTradingEnv(
            data=data,
            window_size=config["env"]["window_size"],
            initial_balance=config["env"]["initial_balance"],
            commission=config["env"]["commission"],
            max_position=config["env"]["max_position"],
            use_pnl=config["env"]["use_pnl"],
            use_sharpe=config["env"]["use_sharpe"],
            use_drawdown=config["env"]["use_drawdown"],
            reward_scale=config["env"]["reward_scale"]
        )
        
        return env
    
    def _create_model(self, config: Dict[str, Any]) -> TCN_CNN_PPO:
        """Create model with given configuration."""
        model = TCN_CNN_PPO(
            obs_dim=config["model"]["obs_dim"],
            action_dim=config["model"]["action_dim"],
            tcn_channels=config["model"]["tcn_channels"],
            cnn_filters=config["model"]["cnn_filters"],
            kernel_size=config["model"]["kernel_size"],
            dropout=config["model"]["dropout"]
        ).to(self.device)
        
        return model
    
    def _train_trial(
        self,
        trial_config: Dict[str, Any],
        model: TCN_CNN_PPO,
        env: CryptoTradingEnv,
        trial_dir: Path,
        trial: optuna.Trial = None
    ) -> Dict[str, float]:
        """Train model for a single trial."""
        # Set up optimizer
        optimizer = optim.Adam(
            model.parameters(),
            lr=trial_config["training"]["learning_rate"],
            eps=1e-5
        )
        
        # Initialize agent
        agent = PPOAgent(
            model=model,
            optimizer=optimizer,
            device=self.device,
            gamma=trial_config["training"]["gamma"],
            epsilon=trial_config["training"]["epsilon"],
            entropy_coef=trial_config["training"]["entropy_coef"],
            ppo_epochs=trial_config["training"]["ppo_epochs"],
            batch_size=trial_config["training"]["batch_size"],
            clip_param=trial_config["training"]["clip_param"]
        )
        
        # Training loop
        best_sharpe = -np.inf
        metrics_history = []
        
        for episode in range(trial_config["training"]["max_episodes"]):
            # Train for one episode
            episode_metrics = self._train_episode(
                agent=agent,
                env=env,
                trial_config=trial_config
            )
            
            # Update metrics history
            metrics_history.append(episode_metrics)
            
            # Report to Optuna
            if trial is not None:
                trial.report(episode_metrics["sharpe_ratio"], episode)
                
                # Handle pruning
                if trial.should_prune():
                    raise optuna.TrialPruned()
            
            # Save best model
            if episode_metrics["sharpe_ratio"] > best_sharpe:
                best_sharpe = episode_metrics["sharpe_ratio"]
                self._save_model(model, trial_dir / "best_model.pt")
        
        # Return best metrics
        return max(metrics_history, key=lambda x: x["sharpe_ratio"])
    
    def _train_episode(
        self,
        agent: PPOAgent,
        env: CryptoTradingEnv,
        trial_config: Dict[str, Any]
    ) -> Dict[str, float]:
        """Train for one episode."""
        obs = env.reset()
        done = False
        episode_reward = 0
        
        while not done:
            # Get action from agent
            action, log_prob, value = agent.act(obs)
            
            # Take step in environment
            next_obs, reward, done, info = env.step(action)
            
            # Store transition
            agent.store_transition(
                state=obs,
                action=action,
                reward=reward,
                next_state=next_obs,
                done=done,
                log_prob=log_prob,
                value=value
            )
            
            # Update observation
            obs = next_obs
            episode_reward += reward
            
            # Update agent
            if len(agent.buffer) >= trial_config["training"]["batch_size"]:
                agent.update()
        
        # Calculate metrics
        metrics = {
            "episode_reward": episode_reward,
            "portfolio_value": info.get("portfolio_value", 0),
            "sharpe_ratio": info.get("sharpe_ratio", 0),
            "max_drawdown": info.get("max_drawdown_pct", 0) / 100,
            "win_rate": info.get("win_rate_pct", 0) / 100,
            "num_trades": info.get("num_trades", 0)
        }
        
        return metrics
    
    def _save_model(self, model: nn.Module, path: Path) -> None:
        """Save model to disk."""
        torch.save({
            "model_state_dict": model.state_dict(),
        }, path)
    
    def optimize(self) -> optuna.Study:
        """Run hyperparameter optimization."""
        # Set random seeds for reproducibility
        torch.manual_seed(self.seed)
        np.random.seed(self.seed)
        
        # Create study
        study = optuna.create_study(
            study_name=self.study_name,
            storage=self.storage,
            load_if_exists=True,
            sampler=TPESampler(seed=self.seed),
            pruner=MedianPruner(n_startup_trials=10, n_warmup_steps=5),
            direction="maximize"
        )
        
        # Run optimization
        study.optimize(
            self._objective,
            n_trials=self.n_trials,
            n_jobs=self.n_jobs,
            show_progress_bar=True
        )
        
        # Save study results
        self._save_study_results(study)
        
        return study
    
    def _save_study_results(self, study: optuna.Study) -> None:
        """Save study results to disk."""
        # Save best parameters
        best_params = study.best_params
        with open(self.study_dir / "best_params.json", "w") as f:
            json.dump(best_params, f, indent=2)
        
        # Save all trials
        trials_df = study.trials_dataframe()
        trials_df.to_csv(self.study_dir / "trials.csv", index=False)
        
        # Save best config
        best_config = self._update_config(study.best_params)
        save_config(best_config, self.study_dir / "best_config.yaml")
        
        print(f"Study results saved to {self.study_dir}")
        print(f"Best trial value: {study.best_trial.value}")
        print(f"Best parameters: {study.best_params}")


def optimize_hyperparameters(
    config_path: str,
    data_loader: CryptoDataLoader,
    study_name: str = "trading_study",
    n_trials: int = 100,
    n_jobs: int = 1,
    seed: int = 42
) -> Dict[str, Any]:
    """
    Run hyperparameter optimization.
    
    Args:
        config_path: Path to base config file
        data_loader: Data loader instance
        study_name: Name of the study
        n_trials: Number of trials to run
        n_jobs: Number of parallel jobs
        seed: Random seed
        
    Returns:
        dict: Best hyperparameters
    """
    # Load base config
    config = load_config(config_path)
    
    # Initialize optimizer
    optimizer = HyperparameterOptimizer(
        config=config,
        data_loader=data_loader,
        study_name=study_name,
        n_trials=n_trials,
        n_jobs=n_jobs,
        seed=seed
    )
    
    # Run optimization
    study = optimizer.optimize()
    
    return study.best_params
