"""
Health Check and Auto-Fix System for Grid Trading Bot
"""
import asyncio
import psutil
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import aiohttp
import sqlite3
import os
import socket
from dataclasses import dataclass, asdict
import json

# Configure logging
logger = logging.getLogger(__name__)

@dataclass
class HealthStatus:
    """Health status container"""
    component: str
    status: str  # 'healthy', 'degraded', 'critical'
    message: str
    timestamp: str
    metrics: Optional[Dict[str, Any]] = None

class HealthMonitor:
    """Monitor and maintain system health"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.last_check = datetime.utcnow()
        self.metrics_history = []
        self.max_history = 1000  # Keep last 1000 metrics
        
    async def check_api_health(self) -> HealthStatus:
        """Check Binance API health"""
        metrics = {}
        try:
            async with aiohttp.ClientSession() as session:
                start_time = datetime.utcnow()
                async with session.get('https://api.binance.com/api/v3/ping') as resp:
                    latency = (datetime.utcnow() - start_time).total_seconds()
                    metrics['latency_ms'] = latency * 1000
                    
                    if resp.status == 200:
                        return HealthStatus(
                            component='api',
                            status='healthy',
                            message='API is responding normally',
                            timestamp=datetime.utcnow().isoformat(),
                            metrics=metrics
                        )
                    else:
                        return HealthStatus(
                            component='api',
                            status='critical',
                            message=f'API returned status {resp.status}',
                            timestamp=datetime.utcnow().isoformat(),
                            metrics=metrics
                        )
        except Exception as e:
            return HealthStatus(
                component='api',
                status='critical',
                message=f'API connection failed: {str(e)}',
                timestamp=datetime.utcnow().isoformat(),
                metrics=metrics
            )
    
    async def check_database_health(self) -> HealthStatus:
        """Check database health"""
        metrics = {}
        try:
            db_path = self.config.get('database', {}).get('path', 'trading_data.db')
            start_time = datetime.utcnow()
            
            # Check if database exists and is accessible
            if not os.path.exists(db_path):
                return HealthStatus(
                    component='database',
                    status='critical',
                    message=f'Database file not found: {db_path}',
                    timestamp=datetime.utcnow().isoformat()
                )
            
            # Test connection and basic query
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT name FROM sqlite_master WHERE type="table"')
                tables = cursor.fetchall()
                metrics['table_count'] = len(tables)
                
                # Check for required tables
                required_tables = ['ohlcv', 'trades', 'orders']
                missing_tables = [t for t in required_tables if t not in [x[0] for x in tables]]
                
                if missing_tables:
                    return HealthStatus(
                        component='database',
                        status='degraded',
                        message=f'Missing tables: {", ".join(missing_tables)}',
                        timestamp=datetime.utcnow().isoformat(),
                        metrics=metrics
                    )
                
                # Check data freshness
                cursor.execute('SELECT MAX(timestamp) FROM ohlcv')
                latest_ts = cursor.fetchone()[0]
                if latest_ts:
                    data_age = (datetime.utcnow() - datetime.fromisoformat(latest_ts)).total_seconds()
                    metrics['data_freshness_seconds'] = data_age
                    
                    if data_age > 3600:  # 1 hour
                        return HealthStatus(
                            component='database',
                            status='degraded',
                            message=f'Data is stale ({data_age/3600:.1f} hours old)',
                            timestamp=datetime.utcnow().isoformat(),
                            metrics=metrics
                        )
            
            metrics['latency_ms'] = (datetime.utcnow() - start_time).total_seconds() * 1000
            return HealthStatus(
                component='database',
                status='healthy',
                message='Database is healthy',
                timestamp=datetime.utcnow().isoformat(),
                metrics=metrics
            )
            
        except Exception as e:
            return HealthStatus(
                component='database',
                status='critical',
                message=f'Database check failed: {str(e)}',
                timestamp=datetime.utcnow().isoformat(),
                metrics=metrics
            )
    
    async def check_system_resources(self) -> HealthStatus:
        """Check system resource usage"""
        metrics = {
            'cpu_percent': psutil.cpu_percent(),
            'memory_percent': psutil.virtual_memory().percent,
            'disk_percent': psutil.disk_usage('/').percent,
            'process_memory_mb': psutil.Process().memory_info().rss / (1024 * 1024)
        }
        
        issues = []
        if metrics['cpu_percent'] > 90:
            issues.append(f'High CPU usage: {metrics["cpu_percent"]:.1f}%')
        if metrics['memory_percent'] > 90:
            issues.append(f'High memory usage: {metrics["memory_percent"]:.1f}%')
        if metrics['disk_percent'] > 90:
            issues.append(f'High disk usage: {metrics["disk_percent"]:.1f}%')
            
        status = 'critical' if any([
            metrics['cpu_percent'] > 95,
            metrics['memory_percent'] > 95,
            metrics['disk_percent'] > 95
        ]) else 'degraded' if issues else 'healthy'
        
        return HealthStatus(
            component='system_resources',
            status=status,
            message='; '.join(issues) if issues else 'System resources normal',
            timestamp=datetime.utcnow().isoformat(),
            metrics=metrics
        )
    
    async def check_trading_health(self) -> HealthStatus:
        """Check trading system health"""
        metrics = {}
        try:
            # Check if we can access trading data
            # Add your specific trading health checks here
            return HealthStatus(
                component='trading',
                status='healthy',
                message='Trading system is healthy',
                timestamp=datetime.utcnow().isoformat(),
                metrics=metrics
            )
        except Exception as e:
            return HealthStatus(
                component='trading',
                status='critical',
                message=f'Trading system error: {str(e)}',
                timestamp=datetime.utcnow().isoformat(),
                metrics=metrics
            )
    
    async def run_health_checks(self) -> Dict[str, HealthStatus]:
        """Run all health checks"""
        self.last_check = datetime.utcnow()
        
        # Run all checks concurrently
        checks = {
            'api': self.check_api_health(),
            'database': self.check_database_health(),
            'system': self.check_system_resources(),
            'trading': self.check_trading_health()
        }
        
        results = {}
        for name, task in checks.items():
            try:
                results[name] = await task
            except Exception as e:
                results[name] = HealthStatus(
                    component=name,
                    status='critical',
                    message=f'Check failed: {str(e)}',
                    timestamp=datetime.utcnow().isoformat()
                )
        
        # Store metrics
        self.metrics_history.append({
            'timestamp': datetime.utcnow().isoformat(),
            'metrics': {k: asdict(v) for k, v in results.items()}
        })
        self.metrics_history = self.metrics_history[-self.max_history:]
        
        return results
    
    async def auto_fix_issues(self, health_status: Dict[str, HealthStatus]) -> Dict[str, str]:
        """Attempt to automatically fix detected issues"""
        fixes = {}
        
        # API Issues
        if health_status['api'].status != 'healthy':
            fixes['api'] = await self.fix_api_issues(health_status['api'])
            
        # Database Issues
        if health_status['database'].status != 'healthy':
            fixes['database'] = await self.fix_database_issues(health_status['database'])
            
        # System Resource Issues
        if health_status['system'].status != 'healthy':
            fixes['system'] = await self.fix_system_issues(health_status['system'])
            
        return fixes
    
    async def fix_api_issues(self, status: HealthStatus) -> str:
        """Fix API-related issues"""
        # TODO: Implement specific API fixes
        return "API issues detected. Please check API keys and network connection."
    
    async def fix_database_issues(self, status: HealthStatus) -> str:
        """Fix database issues"""
        # TODO: Implement specific database fixes
        return "Database issues detected. Please check database connection and permissions."
    
    async def fix_system_issues(self, status: HealthStatus) -> str:
        """Fix system resource issues"""
        metrics = status.metrics or {}
        
        if metrics.get('memory_percent', 0) > 90:
            # Try to free up memory
            import gc
            gc.collect()
            return "Freed up memory by running garbage collection"
            
        return "System resource issues detected. Please check system logs for details."
