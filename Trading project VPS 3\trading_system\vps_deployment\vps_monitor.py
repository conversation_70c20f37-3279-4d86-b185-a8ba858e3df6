#!/usr/bin/env python3
"""
VPS Monitoring and Management System
Monitor live trading system performance and health
"""
import asyncio
import json
import logging
import psutil
import time
import smtplib
from datetime import datetime, timezone
from email.mime.text import MimeText
from email.mime.multipart import <PERSON>me<PERSON>ultipart
from pathlib import Path
from typing import Dict, List, Optional
import subprocess
import os


class VPSMonitor:
    """Comprehensive VPS monitoring system."""
    
    def __init__(self, config_file: str = "monitor_config.json"):
        self.config = self.load_config(config_file)
        self.setup_logging()
        self.alerts_sent = {}
        self.last_health_check = None
        
        self.logger.info("VPS Monitor initialized")
    
    def load_config(self, config_file: str) -> Dict:
        """Load monitoring configuration."""
        default_config = {
            "monitoring": {
                "check_interval": 300,  # 5 minutes
                "alert_cooldown": 1800,  # 30 minutes
                "max_cpu_percent": 80,
                "max_memory_percent": 85,
                "max_disk_percent": 90,
                "min_free_disk_gb": 5
            },
            "trading": {
                "process_name": "live_trading_main.py",
                "log_file": "/home/<USER>/live_trading/logs/trading.log",
                "max_log_age_hours": 2,
                "expected_trade_frequency": 24  # trades per day
            },
            "alerts": {
                "email_enabled": False,
                "smtp_server": "smtp.gmail.com",
                "smtp_port": 587,
                "email_user": "",
                "email_password": "",
                "alert_recipients": []
            },
            "auto_restart": {
                "enabled": True,
                "max_restarts_per_hour": 3,
                "restart_delay": 60
            }
        }
        
        config_path = Path(config_file)
        if config_path.exists():
            with open(config_path, 'r') as f:
                user_config = json.load(f)
                # Merge with defaults
                default_config.update(user_config)
        else:
            # Create default config file
            with open(config_path, 'w') as f:
                json.dump(default_config, f, indent=2)
        
        return default_config
    
    def setup_logging(self):
        """Setup monitoring logging."""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / 'monitor.log'),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger('VPSMonitor')
    
    def check_system_health(self) -> Dict:
        """Check overall system health."""
        health = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'cpu': {
                'percent': psutil.cpu_percent(interval=1),
                'count': psutil.cpu_count(),
                'load_avg': os.getloadavg() if hasattr(os, 'getloadavg') else None
            },
            'memory': {
                'percent': psutil.virtual_memory().percent,
                'available_gb': psutil.virtual_memory().available / (1024**3),
                'total_gb': psutil.virtual_memory().total / (1024**3)
            },
            'disk': {
                'percent': psutil.disk_usage('/').percent,
                'free_gb': psutil.disk_usage('/').free / (1024**3),
                'total_gb': psutil.disk_usage('/').total / (1024**3)
            },
            'network': {
                'bytes_sent': psutil.net_io_counters().bytes_sent,
                'bytes_recv': psutil.net_io_counters().bytes_recv
            }
        }
        
        return health
    
    def check_trading_process(self) -> Dict:
        """Check if trading process is running and healthy."""
        process_name = self.config['trading']['process_name']
        
        status = {
            'running': False,
            'pid': None,
            'cpu_percent': 0,
            'memory_mb': 0,
            'uptime_hours': 0,
            'last_log_entry': None
        }
        
        # Check if process is running
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time']):
            try:
                if any(process_name in cmd for cmd in proc.info['cmdline']):
                    status['running'] = True
                    status['pid'] = proc.info['pid']
                    
                    # Get process details
                    process = psutil.Process(proc.info['pid'])
                    status['cpu_percent'] = process.cpu_percent()
                    status['memory_mb'] = process.memory_info().rss / (1024**2)
                    status['uptime_hours'] = (time.time() - proc.info['create_time']) / 3600
                    break
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        # Check log file
        log_file = Path(self.config['trading']['log_file'])
        if log_file.exists():
            try:
                # Get last log entry
                with open(log_file, 'r') as f:
                    lines = f.readlines()
                    if lines:
                        status['last_log_entry'] = lines[-1].strip()
                
                # Check log age
                log_age = time.time() - log_file.stat().st_mtime
                status['log_age_hours'] = log_age / 3600
                
            except Exception as e:
                self.logger.error(f"Error reading log file: {e}")
        
        return status
    
    def check_trading_performance(self) -> Dict:
        """Check trading performance metrics."""
        performance = {
            'balance_trend': 'unknown',
            'recent_trades': 0,
            'last_trade_time': None,
            'error_rate': 0
        }
        
        try:
            # Read trading log for performance data
            log_file = Path(self.config['trading']['log_file'])
            if log_file.exists():
                # Analyze recent log entries
                with open(log_file, 'r') as f:
                    lines = f.readlines()
                    
                    # Count recent trades and errors
                    recent_lines = lines[-100:]  # Last 100 lines
                    trade_count = sum(1 for line in recent_lines if 'ORDER:' in line)
                    error_count = sum(1 for line in recent_lines if 'ERROR' in line)
                    
                    performance['recent_trades'] = trade_count
                    performance['error_rate'] = error_count / len(recent_lines) if recent_lines else 0
                    
                    # Find last trade
                    for line in reversed(lines):
                        if 'ORDER:' in line:
                            # Extract timestamp from log line
                            try:
                                timestamp_str = line.split(' - ')[0]
                                performance['last_trade_time'] = timestamp_str
                                break
                            except:
                                pass
        
        except Exception as e:
            self.logger.error(f"Error checking trading performance: {e}")
        
        return performance
    
    def detect_issues(self, health: Dict, trading: Dict, performance: Dict) -> List[Dict]:
        """Detect system and trading issues."""
        issues = []
        
        # System health issues
        if health['cpu']['percent'] > self.config['monitoring']['max_cpu_percent']:
            issues.append({
                'type': 'system',
                'severity': 'warning',
                'message': f"High CPU usage: {health['cpu']['percent']:.1f}%"
            })
        
        if health['memory']['percent'] > self.config['monitoring']['max_memory_percent']:
            issues.append({
                'type': 'system',
                'severity': 'warning',
                'message': f"High memory usage: {health['memory']['percent']:.1f}%"
            })
        
        if health['disk']['percent'] > self.config['monitoring']['max_disk_percent']:
            issues.append({
                'type': 'system',
                'severity': 'critical',
                'message': f"High disk usage: {health['disk']['percent']:.1f}%"
            })
        
        if health['disk']['free_gb'] < self.config['monitoring']['min_free_disk_gb']:
            issues.append({
                'type': 'system',
                'severity': 'critical',
                'message': f"Low disk space: {health['disk']['free_gb']:.1f}GB free"
            })
        
        # Trading process issues
        if not trading['running']:
            issues.append({
                'type': 'trading',
                'severity': 'critical',
                'message': "Trading process is not running"
            })
        
        if trading.get('log_age_hours', 0) > self.config['trading']['max_log_age_hours']:
            issues.append({
                'type': 'trading',
                'severity': 'warning',
                'message': f"Trading log is stale: {trading.get('log_age_hours', 0):.1f} hours old"
            })
        
        # Performance issues
        if performance['error_rate'] > 0.1:  # More than 10% error rate
            issues.append({
                'type': 'performance',
                'severity': 'warning',
                'message': f"High error rate: {performance['error_rate']:.1%}"
            })
        
        return issues
    
    def send_alert(self, issues: List[Dict]):
        """Send alert notifications."""
        if not self.config['alerts']['email_enabled']:
            return
        
        # Check cooldown
        current_time = time.time()
        for issue in issues:
            issue_key = f"{issue['type']}_{issue['severity']}"
            last_sent = self.alerts_sent.get(issue_key, 0)
            
            if current_time - last_sent < self.config['alerts']['alert_cooldown']:
                continue  # Skip due to cooldown
            
            # Send email alert
            self.send_email_alert(issues)
            self.alerts_sent[issue_key] = current_time
            break
    
    def send_email_alert(self, issues: List[Dict]):
        """Send email alert."""
        try:
            msg = MimeMultipart()
            msg['From'] = self.config['alerts']['email_user']
            msg['To'] = ', '.join(self.config['alerts']['alert_recipients'])
            msg['Subject'] = f"VPS Trading Alert - {len(issues)} Issues Detected"
            
            # Create email body
            body = f"""
VPS Trading System Alert
========================

Time: {datetime.now(timezone.utc).isoformat()}
Issues Detected: {len(issues)}

Issues:
"""
            
            for issue in issues:
                body += f"- [{issue['severity'].upper()}] {issue['type']}: {issue['message']}\n"
            
            body += "\nPlease check the VPS immediately."
            
            msg.attach(MimeText(body, 'plain'))
            
            # Send email
            server = smtplib.SMTP(self.config['alerts']['smtp_server'], self.config['alerts']['smtp_port'])
            server.starttls()
            server.login(self.config['alerts']['email_user'], self.config['alerts']['email_password'])
            server.send_message(msg)
            server.quit()
            
            self.logger.info(f"Alert email sent for {len(issues)} issues")
            
        except Exception as e:
            self.logger.error(f"Failed to send email alert: {e}")
    
    def auto_restart_trading(self) -> bool:
        """Automatically restart trading process if needed."""
        if not self.config['auto_restart']['enabled']:
            return False
        
        try:
            # Check restart limits
            current_hour = datetime.now().hour
            restart_count = self.get_restart_count(current_hour)
            
            if restart_count >= self.config['auto_restart']['max_restarts_per_hour']:
                self.logger.warning("Max restarts per hour reached, skipping auto-restart")
                return False
            
            # Restart trading process
            self.logger.info("Attempting to restart trading process...")
            
            # Kill existing process
            subprocess.run("pkill -f live_trading_main.py", shell=True)
            
            # Wait before restart
            time.sleep(self.config['auto_restart']['restart_delay'])
            
            # Start new process
            trading_dir = "/home/<USER>/live_trading"
            start_command = f"cd {trading_dir} && nohup {trading_dir}/venv/bin/python live_trading_main.py > logs/restart.log 2>&1 &"
            subprocess.run(start_command, shell=True)
            
            # Log restart
            self.log_restart(current_hour)
            
            self.logger.info("Trading process restarted successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to restart trading process: {e}")
            return False
    
    def get_restart_count(self, hour: int) -> int:
        """Get restart count for current hour."""
        restart_log = Path("logs/restarts.json")
        if not restart_log.exists():
            return 0
        
        try:
            with open(restart_log, 'r') as f:
                restarts = json.load(f)
                return restarts.get(str(hour), 0)
        except:
            return 0
    
    def log_restart(self, hour: int):
        """Log restart event."""
        restart_log = Path("logs/restarts.json")
        
        try:
            if restart_log.exists():
                with open(restart_log, 'r') as f:
                    restarts = json.load(f)
            else:
                restarts = {}
            
            restarts[str(hour)] = restarts.get(str(hour), 0) + 1
            
            with open(restart_log, 'w') as f:
                json.dump(restarts, f)
                
        except Exception as e:
            self.logger.error(f"Failed to log restart: {e}")
    
    async def monitor_loop(self):
        """Main monitoring loop."""
        self.logger.info("Starting VPS monitoring loop...")
        
        while True:
            try:
                # Collect system data
                health = self.check_system_health()
                trading = self.check_trading_process()
                performance = self.check_trading_performance()
                
                # Detect issues
                issues = self.detect_issues(health, trading, performance)
                
                # Log status
                status = {
                    'timestamp': datetime.now(timezone.utc).isoformat(),
                    'health': health,
                    'trading': trading,
                    'performance': performance,
                    'issues': len(issues)
                }
                
                self.logger.info(f"Monitor check: {len(issues)} issues detected")
                
                # Handle issues
                if issues:
                    self.logger.warning(f"Issues detected: {[i['message'] for i in issues]}")
                    
                    # Send alerts
                    self.send_alert(issues)
                    
                    # Auto-restart if trading process is down
                    if any(i['type'] == 'trading' and 'not running' in i['message'] for i in issues):
                        self.auto_restart_trading()
                
                # Save status to file
                status_file = Path("logs/monitor_status.json")
                with open(status_file, 'w') as f:
                    json.dump(status, f, indent=2)
                
                # Wait for next check
                await asyncio.sleep(self.config['monitoring']['check_interval'])
                
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retry


async def main():
    """Main monitoring function."""
    monitor = VPSMonitor()
    await monitor.monitor_loop()


if __name__ == "__main__":
    asyncio.run(main())
