
<!DOCTYPE html>
<html>
<head>
    <title>Cycle 10 - Live Trading Report</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .container { max-width: 1400px; margin: 20px auto; background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); }
        .header { text-align: center; background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px; border-radius: 15px 15px 0 0; }
        .header h1 { margin: 0; font-size: 2.5em; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .content { padding: 30px; }
        .metric-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 30px 0; }
        .metric-card { background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 20px; border-radius: 12px; border-left: 5px solid #28a745; box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        .metric-value { font-size: 2em; font-weight: bold; color: #28a745; margin-bottom: 5px; }
        .metric-label { color: #6c757d; font-weight: 500; }
        .section { margin: 40px 0; }
        .section h2 { color: #2c3e50; border-bottom: 3px solid #28a745; padding-bottom: 15px; font-size: 1.8em; }
        .chart-container { background: #f8f9fa; padding: 20px; border-radius: 12px; margin: 20px 0; box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        .trade-table { width: 100%; border-collapse: collapse; margin: 20px 0; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        .trade-table th { background: linear-gradient(135deg, #343a40 0%, #495057 100%); color: white; padding: 15px; text-align: center; font-weight: 600; }
        .trade-table td { padding: 12px; text-align: center; border-bottom: 1px solid #dee2e6; }
        .trade-table tr:nth-child(even) { background: #f8f9fa; }
        .trade-table tr:hover { background: #e3f2fd; }
        .profit { color: #28a745; font-weight: bold; }
        .loss { color: #dc3545; font-weight: bold; }
        .success-banner { background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); color: #155724; padding: 25px; border-radius: 12px; margin: 20px 0; border-left: 5px solid #28a745; }
        .balance-highlight { font-size: 1.1em; font-weight: bold; background: #e8f5e8; padding: 5px 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Cycle 10 - LIVE TRADING REPORT</h1>
            <p>TCN-CNN-PPO Algorithm Performance Analysis</p>
            <p>Ready for Live Trading Deployment</p>
            <p>Generated: 2025-05-27 01:22:58</p>
        </div>

        <div class="content">
            <div class="success-banner">
                <h3>🎉 EXCEPTIONAL PERFORMANCE ACHIEVED!</h3>
                <p><strong>Composite Score:</strong> 0.7798 | <strong>Return:</strong> +2,403.65% | <strong>Max Drawdown:</strong> 0.99%</p>
                <p>This model demonstrates outstanding risk-adjusted returns and is ready for live trading deployment.</p>
            </div>

            <div class="metric-grid">
                <div class="metric-card">
                    <div class="metric-value">0.7798</div>
                    <div class="metric-label">Composite Score</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">+2,403.65%</div>
                    <div class="metric-label">Total Return</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">0.99%</div>
                    <div class="metric-label">Max Drawdown</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">$7,510.96</div>
                    <div class="metric-label">Final Balance</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">331</div>
                    <div class="metric-label">Total Trades</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">41.7%</div>
                    <div class="metric-label">Win Rate</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">13.76</div>
                    <div class="metric-label">Sortino Ratio</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">1.25</div>
                    <div class="metric-label">Profit Factor</div>
                </div>
            </div>

            <div class="section">
                <h2>📈 Equity Curve</h2>
                <div class="chart-container">
                    <div id="equityCurve" style="width:100%;height:400px;"></div>
                </div>
            </div>

            <div class="section">
                <h2>📊 First 10 Trades - Running Balance</h2>
                <table class="trade-table">
                    <thead>
                        <tr>
                            <th>Trade #</th>
                            <th>Direction</th>
                            <th>Entry Price</th>
                            <th>Exit Price</th>
                            <th>Net P&L</th>
                            <th>Running Balance</th>
                            <th>Exit Reason</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1</td>
                            <td>LONG</td>
                            <td>$84281.90</td>
                            <td>$83851.40</td>
                            <td class="loss">$-15.03</td>
                            <td class="balance-highlight">$284.97</td>
                            <td>STOP_LOSS</td>
                        </tr>
                        <tr>
                            <td>2</td>
                            <td>SHORT</td>
                            <td>$83851.40</td>
                            <td>$84166.30</td>
                            <td class="loss">$-14.28</td>
                            <td class="balance-highlight">$270.69</td>
                            <td>STOP_LOSS</td>
                        </tr>
                        <tr>
                            <td>3</td>
                            <td>LONG</td>
                            <td>$84166.30</td>
                            <td>$84012.64</td>
                            <td class="loss">$-13.56</td>
                            <td class="balance-highlight">$257.13</td>
                            <td>STOP_LOSS</td>
                        </tr>
                        <tr>
                            <td>4</td>
                            <td>SHORT</td>
                            <td>$84012.64</td>
                            <td>$83787.28</td>
                            <td class="profit">$+25.69</td>
                            <td class="balance-highlight">$282.82</td>
                            <td>TAKE_PROFIT</td>
                        </tr>
                        <tr>
                            <td>5</td>
                            <td>LONG</td>
                            <td>$83787.28</td>
                            <td>$83544.80</td>
                            <td class="loss">$-14.17</td>
                            <td class="balance-highlight">$268.65</td>
                            <td>STOP_LOSS</td>
                        </tr>
                        <tr>
                            <td>6</td>
                            <td>LONG</td>
                            <td>$82293.56</td>
                            <td>$82851.95</td>
                            <td class="profit">$+26.84</td>
                            <td class="balance-highlight">$295.49</td>
                            <td>TAKE_PROFIT</td>
                        </tr>
                        <tr>
                            <td>7</td>
                            <td>SHORT</td>
                            <td>$82504.01</td>
                            <td>$82198.43</td>
                            <td class="profit">$+29.52</td>
                            <td class="balance-highlight">$325.01</td>
                            <td>TAKE_PROFIT</td>
                        </tr>
                        <tr>
                            <td>8</td>
                            <td>SHORT</td>
                            <td>$82198.43</td>
                            <td>$82332.63</td>
                            <td class="loss">$-16.28</td>
                            <td class="balance-highlight">$308.72</td>
                            <td>STOP_LOSS</td>
                        </tr>
                        <tr>
                            <td>9</td>
                            <td>LONG</td>
                            <td>$82332.63</td>
                            <td>$82758.40</td>
                            <td class="profit">$+30.84</td>
                            <td class="balance-highlight">$339.57</td>
                            <td>TAKE_PROFIT</td>
                        </tr>
                        <tr>
                            <td>10</td>
                            <td>SHORT</td>
                            <td>$82758.40</td>
                            <td>$82511.17</td>
                            <td class="profit">$+33.92</td>
                            <td class="balance-highlight">$373.49</td>
                            <td>TAKE_PROFIT</td>
                        </tr>
                    </tbody>
                </table>
                <p><strong>Note:</strong> Showing first 10 trades of 331 total trades.</p>
            </div>

            <div class="section">
                <h2>🎯 Top 5 Winning Trades</h2>
                <table class="trade-table">
                    <thead>
                        <tr>
                            <th>Rank</th>
                            <th>Direction</th>
                            <th>Net P&L</th>
                            <th>Entry Price</th>
                            <th>Exit Price</th>
                            <th>Return %</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1</td>
                            <td>SHORT</td>
                            <td class="profit">$+1116.79</td>
                            <td>$93678.13</td>
                            <td>$92992.33</td>
                            <td class="profit">-0.73%</td>
                        </tr>
                        <tr>
                            <td>2</td>
                            <td>LONG</td>
                            <td class="profit">$+1077.04</td>
                            <td>$93101.83</td>
                            <td>$93346.08</td>
                            <td class="profit">+0.26%</td>
                        </tr>
                        <tr>
                            <td>3</td>
                            <td>LONG</td>
                            <td class="profit">$+1068.91</td>
                            <td>$93427.98</td>
                            <td>$93980.47</td>
                            <td class="profit">+0.59%</td>
                        </tr>
                        <tr>
                            <td>4</td>
                            <td>LONG</td>
                            <td class="profit">$+979.21</td>
                            <td>$92777.27</td>
                            <td>$93101.83</td>
                            <td class="profit">+0.35%</td>
                        </tr>
                        <tr>
                            <td>5</td>
                            <td>LONG</td>
                            <td class="profit">$+910.75</td>
                            <td>$92940.06</td>
                            <td>$93195.28</td>
                            <td class="profit">+0.27%</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="section">
                <h2>📉 Top 5 Losing Trades</h2>
                <table class="trade-table">
                    <thead>
                        <tr>
                            <th>Rank</th>
                            <th>Direction</th>
                            <th>Net P&L</th>
                            <th>Entry Price</th>
                            <th>Exit Price</th>
                            <th>Return %</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1</td>
                            <td>SHORT</td>
                            <td class="loss">$-616.02</td>
                            <td>$92992.33</td>
                            <td>$93311.83</td>
                            <td class="loss">+0.34%</td>
                        </tr>
                        <tr>
                            <td>2</td>
                            <td>SHORT</td>
                            <td class="loss">$-594.10</td>
                            <td>$93011.28</td>
                            <td>$93380.00</td>
                            <td class="loss">+0.40%</td>
                        </tr>
                        <tr>
                            <td>3</td>
                            <td>LONG</td>
                            <td class="loss">$-589.61</td>
                            <td>$93896.28</td>
                            <td>$93678.13</td>
                            <td class="loss">-0.23%</td>
                        </tr>
                        <tr>
                            <td>4</td>
                            <td>LONG</td>
                            <td class="loss">$-585.16</td>
                            <td>$93311.83</td>
                            <td>$93058.58</td>
                            <td class="loss">-0.27%</td>
                        </tr>
                        <tr>
                            <td>5</td>
                            <td>LONG</td>
                            <td class="loss">$-564.33</td>
                            <td>$93556.12</td>
                            <td>$93390.66</td>
                            <td class="loss">-0.18%</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <script>
        var trace1 = {
            x: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331],
            y: [300.0, 284.97, 270.69300300000003, 257.1312835497, 282.81869877631505, 268.6494819676217, 295.4875652161871, 325.00677298128414, 308.7239336549218, 339.56545462704844, 373.4880435442906, 410.7994990943652, 390.2184441897375, 370.66850013583166, 352.0980082790265, 334.4578980642473, 317.7015573712285, 301.78470934692996, 331.93300181068827, 365.09310869157605, 346.80194394612806, 381.44745814634626, 419.55405921516626, 461.4675097307614, 507.56811395286445, 558.2741685367556, 614.0457579735775, 583.2820654991012, 554.0596340175963, 609.4101914559542, 670.290269582404, 636.7087270763255, 604.8096198498016, 574.5086578953266, 545.7257741347707, 518.3849128506188, 492.41382871680275, 541.6059702056114, 514.4715110983102, 565.8672150570314, 537.5172675826742, 510.58765247678224, 561.5953589592128, 533.4594314753563, 586.7520286797444, 557.3557520428892, 529.4322288655404, 502.90767419937686, 477.7119997219881, 525.4354284942148, 577.9264278007868, 548.9723137679673, 603.8146479133873, 573.5635340529266, 630.8625311048139, 599.2563182964627, 569.2335767498099, 626.1000110671159, 688.6474021727208, 757.4432776497756, 719.4953694395218, 683.4486514306018, 649.2078739939286, 714.0637406059221, 678.2891472015655, 746.0502330070019, 820.5806512844014, 779.4695606550529, 740.4181356662348, 703.3231870693564, 668.0866953971816, 634.6155519577828, 602.8213128046979, 663.0431619538872, 629.8246995399975, 598.2704820930436, 658.0377032541387, 625.0700143211063, 593.7540066036189, 564.0069308727776, 535.7501836360515, 508.9090994358853, 483.41275355414746, 459.19377460108467, 436.18816649357035, 414.3351393522425, 455.72721977353154, 501.2543690289074, 476.1415251405591, 523.7080635021009, 497.4702895206457, 547.1675714437582, 519.754476114426, 493.71477686109324, 468.97966654035247, 515.8307352277337, 489.98761539282424, 538.9373781705674, 511.93661552422196, 563.0790834150918, 619.3306838482595, 588.3022165874617, 558.8282755364298, 614.6552202625192, 676.0592767667448, 743.5975985157426, 817.8829986074652, 899.589510168351, 989.4585022341693, 1088.3054066073628, 1033.781305736334, 981.9888623189436, 932.7912203167645, 886.0583801788946, 974.5756123587662, 925.749374179592, 879.3693305331944, 835.3129270734814, 793.4637494271, 872.7307779948673, 829.0069660173244, 787.4737170198565, 748.0212837971617, 822.7486100484981, 904.9411961923431, 859.6036422631067, 816.5374997857251, 898.1095960143191, 987.8307446561496, 938.3404243488765, 891.3295690889978, 980.3733930409887, 931.2566860496352, 1024.2892289859938, 1126.6157229616945, 1070.1722752413136, 1177.0824855379208, 1294.6730258431592, 1229.809907248417, 1168.1964308952713, 1109.6697897074182, 1054.0753332430766, 1001.2661590475984, 1101.2926483364536, 1211.3117839052652, 1150.6250635316114, 1265.5725073784195, 1202.1673247587607, 1141.9387417883468, 1084.7276108247506, 1030.3827575224307, 1133.3179949989214, 1076.5387634494755, 1022.6041714006568, 971.371702413484, 1068.411735484591, 1175.1460678595015, 1116.2712498597405, 1060.3460602417674, 1166.27463165992, 1107.844272613758, 1218.5179154478724, 1157.470167883934, 1273.101437655539, 1209.3190556289965, 1148.7321709419837, 1263.4905148190878, 1389.7132172495146, 1528.5455676527413, 1451.965434713339, 1597.0167816412015, 1756.5587581271575, 1668.555164344987, 1584.960550611303, 1505.5540270256768, 1655.958874325542, 1821.3891658706636, 1730.1375686605434, 1643.45767647065, 1807.639098350068, 1988.2222442752397, 2186.845646478336, 2077.2846795897717, 1973.2127171423242, 1874.3547600134937, 1780.4495865368176, 1958.3165002318456, 1860.2048435702302, 2046.0393074428962, 1943.532738140007, 1846.1617479591928, 2030.593306580316, 1928.8605819206423, 1832.224666766418, 2015.2639109763832, 1914.2991890364663, 2105.537678021209, 2000.0502403523465, 2199.855259363546, 2089.642510869432, 1984.9514210748735, 2183.248068040253, 2401.3545500374744, 2641.249869586218, 2905.1107315578815, 2759.5646839068318, 3035.2451958291244, 3338.466190892454, 3171.209034728742, 3488.0128172981435, 3313.2633751515064, 3147.268880056416, 3461.681041174052, 3288.250821011232, 3616.7470780302538, 3435.5480494209382, 3263.4270921449493, 3589.44345865023, 3409.6123413718533, 3238.7907630691234, 3076.5273458393604, 2922.393325812808, 2775.9814201895865, 3053.301964066526, 3358.326830276772, 3693.8236806214213, 3508.763114222288, 3859.2885493330946, 4244.831475411471, 4668.890139805077, 4434.9787438008425, 4212.78630873642, 4001.7257146687257, 4401.498113564131, 4841.207775109187, 4598.663265576217, 4368.270235970848, 4149.4198971487085, 3941.533960301558, 3744.06310889045, 3556.485547135039, 3378.3056212235733, 3715.7983527838082, 3529.6368553093394, 3882.2475771547424, 3687.7469735392897, 3502.990850164971, 3327.491008571706, 3160.7837090422636, 3476.5460015755857, 3302.3710468966487, 3632.277914481624, 3995.1424781383384, 4394.257211704358, 4833.243507153624, 4591.098007445227, 4361.0839972722215, 4796.756288599717, 5275.952241830829, 5011.627034515104, 4760.544520085898, 4522.041239629594, 4973.793159468591, 4724.606122179214, 4487.903355458036, 4936.244900668294, 5429.375766245056, 5971.7704052929375, 6568.350268781702, 6239.275920315738, 6862.579584755281, 6518.764347559041, 7169.988905880189, 6810.772461695592, 6469.552761364643, 6145.428168020274, 5837.542216802458, 5545.081351740655, 5267.272776018448, 5003.3824099399235, 5503.220312692922, 5227.508975027006, 4965.610775378153, 5461.675291838431, 5188.0453597173255, 5706.331091153086, 6276.39356715928, 5961.9462494446, 6557.544679764115, 6229.011691307933, 6851.289959269595, 7535.733826200627, 8288.55363543807, 9116.580143618332, 10027.326499965804, 9524.957442317518, 9047.757074457411, 8594.464445027095, 8163.881776331238, 8979.453565786729, 8529.582942140814, 9381.688278060681, 8911.66569532984, 9801.941098293291, 10781.155014012791, 11858.192399912668, 11264.096960677043, 10699.765702947123, 11768.67229667154, 11179.************, 12295.************, 11679.************, 11094.***********, 10538.***********, 10010.************, 9509.************, 9032.************, 9935.************, 9437.************, 8964.************, 8515.************, 8088.************, 7683.*************, 8451.************, 9295.***********, 8829.************, 8387.************, 7967.************, 7568.************, 7188.************, 7907.************, 7510.************],
            type: 'scatter',
            mode: 'lines',
            name: 'Account Balance',
            line: {color: '#28a745', width: 3}
        };

        var layout = {
            title: 'Equity Curve - Account Balance Over Time',
            xaxis: {title: 'Trade Number'},
            yaxis: {title: 'Account Balance ($)'},
            showlegend: true,
            margin: {l: 60, r: 30, t: 60, b: 60}
        };

        Plotly.newPlot('equityCurve', [trace1], layout);
        </script>
    </div>
</body>
</html>
        