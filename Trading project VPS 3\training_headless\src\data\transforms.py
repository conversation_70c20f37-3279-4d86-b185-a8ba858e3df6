"""Data transformations and augmentations for time series data."""

import numpy as np
import pandas as pd
import talib
from typing import List, Dict, Any, Optional, Union, Callable
import random

class Compose:
    """Compose several transforms together."""
    
    def __init__(self, transforms: List[Callable]):
        """
        Initialize the transform composition.
        
        Args:
            transforms: List of transform functions to apply
        """
        self.transforms = transforms
    
    def __call__(self, data: np.ndarray) -> np.ndarray:
        """Apply transforms sequentially."""
        for transform in self.transforms:
            data = transform(data)
        return data


class AddIndicators:
    """Add technical indicators to the data."""
    
    def __init__(self, indicators: List[str]):
        """
        Initialize the indicator transformer.
        
        Args:
            indicators: List of indicator names to add (e.g., ['sma_20', 'rsi_14'])
        """
        self.indicators = indicators
    
    def __call__(self, data: np.ndarray) -> np.ndarray:
        """
        Add indicators to the data.
        
        Args:
            data: Input data with shape (seq_len, n_features)
            
        Returns:
            np.ndarray: Data with added indicators
        """
        if len(data) == 0:
            return data
            
        # Convert to DataFrame for easier manipulation
        df = pd.DataFrame(data, columns=['open', 'high', 'low', 'close', 'volume'])
        
        # Add indicators
        for indicator in self.indicators:
            try:
                if indicator.startswith('sma_'):
                    period = int(indicator.split('_')[1])
                    df[f'sma_{period}'] = talib.SMA(df['close'], timeperiod=period)
                
                elif indicator.startswith('ema_'):
                    period = int(indicator.split('_')[1])
                    df[f'ema_{period}'] = talib.EMA(df['close'], timeperiod=period)
                
                elif indicator.startswith('rsi_'):
                    period = int(indicator.split('_')[1])
                    df[f'rsi_{period}'] = talib.RSI(df['close'], timeperiod=period)
                
                elif indicator.startswith('bb_'):
                    parts = indicator.split('_')
                    period = int(parts[1])
                    std_dev = float(parts[2]) if len(parts) > 2 else 2.0
                    upper, middle, lower = talib.BBANDS(
                        df['close'], 
                        timeperiod=period, 
                        nbdevup=std_dev, 
                        nbdevdn=std_dev
                    )
                    df[f'bb_upper_{period}'] = upper
                    df[f'bb_middle_{period}'] = middle
                    df[f'bb_lower_{period}'] = lower
                
                elif indicator.startswith('atr_'):
                    period = int(indicator.split('_')[1])
                    df[f'atr_{period}'] = talib.ATR(
                        df['high'], 
                        df['low'], 
                        df['close'], 
                        timeperiod=period
                    )
                
                elif indicator.startswith('macd_'):
                    fast, slow, signal = map(int, indicator.split('_')[1:])
                    macd, macd_signal, _ = talib.MACD(
                        df['close'], 
                        fastperiod=fast, 
                        slowperiod=slow, 
                        signalperiod=signal
                    )
                    df['macd'] = macd
                    df['macd_signal'] = macd_signal
                
                elif indicator == 'adx':
                    df['adx'] = talib.ADX(
                        df['high'], 
                        df['low'], 
                        df['close'], 
                        timeperiod=14
                    )
                
                elif indicator == 'obv':
                    df['obv'] = talib.OBV(df['close'], df['volume'])
                
            except Exception as e:
                print(f"Error adding indicator {indicator}: {e}")
        
        # Fill NaN values
        df = df.ffill().bfill()
        
        return df.values.astype(np.float32)


class Normalize:
    """Normalize the data."""
    
    def __init__(self, method: str = 'minmax', stats: Dict[str, Any] = None):
        """
        Initialize the normalizer.
        
        Args:
            method: Normalization method ('minmax' or 'standard')
            stats: Precomputed statistics for normalization
        """
        self.method = method.lower()
        self.stats = stats or {}
    
    def __call__(self, data: np.ndarray) -> np.ndarray:
        """
        Normalize the data.
        
        Args:
            data: Input data with shape (seq_len, n_features)
            
        Returns:
            np.ndarray: Normalized data
        """
        if len(data) == 0:
            return data
            
        if self.method == 'minmax':
            if 'min' not in self.stats or 'max' not in self.stats:
                self.stats['min'] = data.min(axis=0, keepdims=True)
                self.stats['max'] = data.max(axis=0, keepdims=True)
                
                # Avoid division by zero
                self.stats['max'][self.stats['max'] == self.stats['min']] = 1.0
            
            normalized = (data - self.stats['min']) / (self.stats['max'] - self.stats['min'])
            
        elif self.method == 'standard':
            if 'mean' not in self.stats or 'std' not in self.stats:
                self.stats['mean'] = data.mean(axis=0, keepdims=True)
                self.stats['std'] = data.std(axis=0, keepdims=True)
                
                # Avoid division by zero
                self.stats['std'][self.stats['std'] == 0] = 1.0
            
            normalized = (data - self.stats['mean']) / self.stats['std']
            
        else:
            raise ValueError(f"Unknown normalization method: {self.method}")
        
        return normalized.astype(np.float32)


class ToTensor:
    """Convert numpy arrays to PyTorch tensors."""
    
    def __call__(self, data: np.ndarray) -> torch.Tensor:
        """
        Convert numpy array to PyTorch tensor.
        
        Args:
            data: Input data
            
        Returns:
            torch.Tensor: Tensor with the same data
        """
        if isinstance(data, torch.Tensor):
            return data
        return torch.from_numpy(data)


class RandomTimeShift:
    """Randomly shift the time series in time."""
    
    def __init__(self, prob: float = 0.5, max_shift: int = 5):
        """
        Initialize the time shift transform.
        
        Args:
            prob: Probability of applying the transform
            max_shift: Maximum number of time steps to shift
        """
        self.prob = prob
        self.max_shift = max_shift
    
    def __call__(self, data: np.ndarray) -> np.ndarray:
        """
        Apply random time shift to the data.
        
        Args:
            data: Input data with shape (seq_len, n_features)
            
        Returns:
            np.ndarray: Shifted data
        """
        if random.random() > self.prob or len(data) == 0:
            return data
        
        shift = random.randint(-self.max_shift, self.max_shift)
        if shift == 0:
            return data
        
        if shift > 0:
            # Shift right (pad with last value)
            shifted = np.concatenate([
                np.tile(data[[0]], (shift, 1)),
                data[:-shift]
            ])
        else:
            # Shift left (pad with first value)
            shifted = np.concatenate([
                data[-shift:],
                np.tile(data[[-1]], (-shift, 1))
            ])
        
        return shifted.astype(np.float32)


class RandomNoise:
    """Add random noise to the data."""
    
    def __init__(self, prob: float = 0.5, scale: float = 0.01):
        """
        Initialize the noise transform.
        
        Args:
            prob: Probability of applying the transform
            scale: Scale of the noise (standard deviation)
        """
        self.prob = prob
        self.scale = scale
    
    def __call__(self, data: np.ndarray) -> np.ndarray:
        """
        Add random noise to the data.
        
        Args:
            data: Input data with shape (seq_len, n_features)
            
        Returns:
            np.ndarray: Noisy data
        """
        if random.random() > self.prob or len(data) == 0:
            return data
        
        noise = np.random.normal(scale=self.scale, size=data.shape)
        return (data + noise).astype(np.float32)
