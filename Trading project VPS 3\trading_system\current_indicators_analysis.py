"""
Current Indicators and Metrics Analysis
Detailed analysis of what's actually implemented vs what was assumed in the report
"""
import sys
import numpy as np
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent))

from src.ml.integrated_training import TradingFeatureExtractor


def analyze_current_implementation():
    """Analyze what indicators and metrics are currently implemented."""
    print("🔍 CURRENT INDICATORS & METRICS ANALYSIS")
    print("=" * 60)
    
    # Analyze TradingFeatureExtractor
    print("\n📊 CURRENT FEATURE EXTRACTION ANALYSIS")
    print("-" * 50)
    
    extractor = TradingFeatureExtractor(lookback_window=24)
    
    # Create sample candle data to analyze feature extraction
    class MockCandle:
        def __init__(self, open_price, high, low, close, volume):
            self.open = open_price
            self.high = high
            self.low = low
            self.close = close
            self.volume = volume
    
    # Create 24 sample candles
    sample_candles = []
    base_price = 50000
    for i in range(24):
        price_change = np.random.normal(0, 0.01)  # 1% volatility
        open_price = base_price * (1 + price_change)
        high = open_price * (1 + abs(np.random.normal(0, 0.005)))
        low = open_price * (1 - abs(np.random.normal(0, 0.005)))
        close = open_price * (1 + np.random.normal(0, 0.005))
        volume = np.random.uniform(100, 1000)
        
        sample_candles.append(MockCandle(open_price, high, low, close, volume))
        base_price = close
    
    # Extract features
    features = extractor.extract_features(sample_candles)
    
    print(f"✅ Feature Extractor Analysis:")
    print(f"   Lookback Window: {extractor.lookback_window} hours")
    print(f"   Total Feature Dimension: {len(features)}")
    print(f"   Features per Candle: {len(features) // extractor.lookback_window}")
    
    # Analyze feature structure
    features_per_candle = len(features) // extractor.lookback_window
    
    print(f"\n📋 CURRENT FEATURES PER CANDLE:")
    print("-" * 40)
    
    feature_names = [
        "Open Price",
        "High Price", 
        "Low Price",
        "Close Price",
        "Volume",
        "High/Open Ratio",
        "Low/Open Ratio", 
        "Close/Open Ratio",
        "Range/Open Ratio"
    ]
    
    for i, name in enumerate(feature_names):
        print(f"   {i+1}. {name}")
    
    print(f"\n🔍 FEATURE ANALYSIS:")
    print(f"   ❌ NO Technical Indicators (RSI, MACD, Bollinger Bands)")
    print(f"   ❌ NO Momentum Indicators")
    print(f"   ❌ NO Trend Indicators")
    print(f"   ❌ NO Volume Indicators")
    print(f"   ❌ NO Volatility Indicators")
    print(f"   ✅ Basic OHLCV data only")
    print(f"   ✅ Simple price ratios")
    
    # Analyze what's missing
    print(f"\n🚨 CRITICAL GAPS IDENTIFIED:")
    print("-" * 40)
    
    missing_indicators = [
        "RSI (Relative Strength Index)",
        "MACD (Moving Average Convergence Divergence)",
        "Bollinger Bands",
        "Moving Averages (SMA, EMA)",
        "ATR (Average True Range)",
        "ADX (Average Directional Index)",
        "Stochastic Oscillator",
        "Williams %R",
        "CCI (Commodity Channel Index)",
        "OBV (On-Balance Volume)",
        "VWAP (Volume Weighted Average Price)",
        "Momentum indicators",
        "Rate of Change (ROC)",
        "Money Flow Index (MFI)"
    ]
    
    for i, indicator in enumerate(missing_indicators, 1):
        print(f"   {i:2d}. ❌ {indicator}")
    
    # Analyze current metrics
    print(f"\n📊 CURRENT METRICS ANALYSIS")
    print("-" * 40)
    
    print(f"✅ IMPLEMENTED METRICS:")
    print(f"   • Total Return (%)")
    print(f"   • Final Balance ($)")
    print(f"   • Total Trades")
    print(f"   • Win Rate (%)")
    print(f"   • Max Drawdown (%)")
    print(f"   • Composite Score (Return 40% + Drawdown 30% + Activity 30%)")
    
    print(f"\n❌ MISSING CRITICAL METRICS:")
    missing_metrics = [
        "Sharpe Ratio",
        "Sortino Ratio", 
        "Calmar Ratio",
        "Maximum Consecutive Losses",
        "Average Win/Loss Ratio",
        "Profit Factor",
        "Recovery Factor",
        "Expectancy",
        "Standard Deviation of Returns",
        "Beta (market correlation)",
        "Alpha (excess return)",
        "Information Ratio",
        "Treynor Ratio",
        "Value at Risk (VaR)",
        "Conditional VaR"
    ]
    
    for i, metric in enumerate(missing_metrics, 1):
        print(f"   {i:2d}. {metric}")
    
    # Analyze reward function
    print(f"\n🎯 CURRENT REWARD FUNCTION ANALYSIS")
    print("-" * 40)
    
    print(f"✅ CURRENT REWARD CALCULATION:")
    print(f"   reward = balance_change / initial_capital")
    print(f"   • Simple balance change normalization")
    print(f"   • No exploration bonuses")
    print(f"   • No profit bonuses")
    print(f"   • No risk adjustment")
    
    print(f"\n❌ REWARD FUNCTION ISSUES:")
    reward_issues = [
        "No incentive for trading activity",
        "No bonus for profitable trades",
        "No penalty for excessive risk",
        "No consideration of market conditions",
        "No exploration encouragement",
        "No multi-objective optimization",
        "No risk-adjusted returns",
        "No drawdown penalties"
    ]
    
    for i, issue in enumerate(reward_issues, 1):
        print(f"   {i}. {issue}")
    
    # Feature scaling analysis
    print(f"\n📏 FEATURE SCALING ANALYSIS")
    print("-" * 40)
    
    sample_features = features[:9]  # First candle features
    print(f"Sample feature values:")
    for i, (name, value) in enumerate(zip(feature_names, sample_features)):
        print(f"   {name}: {value:.2f}")
    
    print(f"\n🔍 SCALING ISSUES:")
    print(f"   ❌ Price values (50,000+) vs ratios (1.0+) - huge scale differences")
    print(f"   ❌ Volume values vary dramatically")
    print(f"   ❌ No normalization applied")
    print(f"   ❌ Features not standardized")
    
    # Generate corrected report
    print(f"\n📄 CORRECTED ANALYSIS SUMMARY")
    print("=" * 50)
    
    print(f"🚨 CRITICAL FINDING: The report made incorrect assumptions!")
    print(f"\n❌ WHAT THE REPORT ASSUMED:")
    print(f"   • Technical indicators were implemented")
    print(f"   • Advanced metrics were available") 
    print(f"   • Proper feature engineering was done")
    
    print(f"\n✅ ACTUAL CURRENT STATE:")
    print(f"   • Only basic OHLCV + simple ratios")
    print(f"   • No technical indicators at all")
    print(f"   • Basic metrics only")
    print(f"   • No feature scaling/normalization")
    print(f"   • Extremely simple reward function")
    
    print(f"\n🎯 ROOT CAUSE CONFIRMED:")
    print(f"   The model has NO MEANINGFUL FEATURES to learn from!")
    print(f"   It's trying to trade with just raw prices and basic ratios.")
    print(f"   No wonder it can't distinguish market conditions!")
    
    return {
        'current_features': feature_names,
        'missing_indicators': missing_indicators,
        'missing_metrics': missing_metrics,
        'feature_dimension': len(features),
        'features_per_candle': features_per_candle
    }


def generate_corrected_report(analysis_results):
    """Generate corrected HTML report with actual findings."""
    
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>CORRECTED: Current Indicators & Metrics Analysis</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }}
            .container {{ max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }}
            .header {{ text-align: center; color: #e74c3c; border-bottom: 3px solid #e74c3c; padding-bottom: 20px; }}
            .section {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }}
            .critical {{ background: #ffebee; border-left: 5px solid #f44336; }}
            .current {{ background: #e8f5e8; border-left: 5px solid #4caf50; }}
            .missing {{ background: #fff3e0; border-left: 5px solid #ff9800; }}
            .feature-list {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 10px; }}
            .feature-item {{ background: #f8f9fa; padding: 10px; border-radius: 5px; }}
            .missing-item {{ background: #ffebee; padding: 10px; border-radius: 5px; }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🔍 CORRECTED: Current Indicators & Metrics Analysis</h1>
                <p>Actual Implementation vs Report Assumptions</p>
            </div>
            
            <div class="section critical">
                <h2>🚨 CRITICAL CORRECTION</h2>
                <p><strong>The previous report made incorrect assumptions about what was implemented!</strong></p>
                <p>This analysis reveals the ACTUAL current state of the system.</p>
            </div>
            
            <div class="section current">
                <h2>✅ ACTUALLY IMPLEMENTED FEATURES</h2>
                <p><strong>Current Features per Candle: {analysis_results['features_per_candle']}</strong></p>
                <div class="feature-list">
                    {chr(10).join([f'<div class="feature-item">✅ {feature}</div>' for feature in analysis_results['current_features']])}
                </div>
                <p><strong>Total Feature Dimension: {analysis_results['feature_dimension']} (24 hours × 9 features)</strong></p>
            </div>
            
            <div class="section missing">
                <h2>❌ MISSING TECHNICAL INDICATORS</h2>
                <p><strong>The model has NO technical indicators - this explains everything!</strong></p>
                <div class="feature-list">
                    {chr(10).join([f'<div class="missing-item">❌ {indicator}</div>' for indicator in analysis_results['missing_indicators'][:10]])}
                </div>
                <p><em>...and {len(analysis_results['missing_indicators']) - 10} more missing indicators</em></p>
            </div>
            
            <div class="section missing">
                <h2>❌ MISSING ADVANCED METRICS</h2>
                <div class="feature-list">
                    {chr(10).join([f'<div class="missing-item">❌ {metric}</div>' for metric in analysis_results['missing_metrics'][:8]])}
                </div>
                <p><em>...and {len(analysis_results['missing_metrics']) - 8} more missing metrics</em></p>
            </div>
            
            <div class="section critical">
                <h2>🎯 ROOT CAUSE CONFIRMED</h2>
                <ul>
                    <li><strong>No Technical Indicators:</strong> Model can't detect trends, momentum, or market conditions</li>
                    <li><strong>Raw Price Data Only:</strong> Huge scaling issues (prices ~50,000 vs ratios ~1.0)</li>
                    <li><strong>No Feature Engineering:</strong> Model has no meaningful patterns to learn</li>
                    <li><strong>Simple Reward Function:</strong> No incentives for proper trading behavior</li>
                </ul>
            </div>
            
            <div class="section">
                <h2>🔧 IMMEDIATE ACTIONS REQUIRED</h2>
                <ol>
                    <li><strong>Add Technical Indicators:</strong> RSI, MACD, Bollinger Bands, Moving Averages</li>
                    <li><strong>Implement Feature Scaling:</strong> Normalize all features to similar ranges</li>
                    <li><strong>Enhanced Reward Function:</strong> Add trading incentives and exploration bonuses</li>
                    <li><strong>Advanced Metrics:</strong> Sharpe ratio, Sortino ratio, proper risk metrics</li>
                    <li><strong>Feature Engineering:</strong> Momentum, trend, volatility indicators</li>
                </ol>
            </div>
        </div>
    </body>
    </html>
    """
    
    Path("reports").mkdir(exist_ok=True)
    with open("reports/corrected_indicators_analysis.html", "w", encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"\n📄 Corrected analysis report saved to: reports/corrected_indicators_analysis.html")


if __name__ == "__main__":
    results = analyze_current_implementation()
    generate_corrected_report(results)
