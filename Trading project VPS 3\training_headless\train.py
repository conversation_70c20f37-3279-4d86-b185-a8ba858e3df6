"""Training script for the TCN-CNN-PPO trading model."""

import os
import time
import json
import argparse
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Tu<PERSON>, Optional

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.tensorboard import SummaryWriter
import yaml

from src.models.tcn_cnn_ppo import TCN_CNN_PPO
from src.agent.ppo_agent import PPOAgent
from src.env.trading_env import CryptoTradingEnv
from src.data.data_loader import CryptoDataLoader
from src.data.transforms import Compose, AddIndicators, Normalize
from src.config import load_config, save_config


def setup_environment(config: Dict[str, Any]) -> Tuple[CryptoTradingEnv, dict]:
    """Set up the trading environment.
    
    Args:
        config: Configuration dictionary
        
    Returns:
        Tuple containing environment and data scaler
    """
    # Initialize data loader
    data_loader = CryptoDataLoader(
        data_dir=config["data"]["data_dir"],
        symbols=config["data"]["symbols"],
        timeframe=config["data"]["timeframe"],
        indicators=config["data"].get("indicators", [])
    )
    
    # Load and prepare data
    data = data_loader.load_data(
        symbol=config["data"]["symbols"][0],  # Use first symbol for training
        start_date=config["data"]["train_start"],
        end_date=config["data"]["train_end"]
    )
    
    # Setup transforms
    transforms = Compose([
        AddIndicators(config["data"].get("indicators", [])),
        Normalize(method="standard")
    ])
    
    # Apply transforms
    scaled_data = transforms(data.values.astype(np.float32))
    
    # Create environment
    env = CryptoTradingEnv(
        data=scaled_data,
        window_size=config["env"]["window_size"],
        initial_balance=config["env"]["initial_balance"],
        commission=config["env"]["commission"],
        max_position=config["env"]["max_position"],
        use_pnl=config["env"]["use_pnl"],
        use_sharpe=config["env"]["use_sharpe"],
        use_drawdown=config["env"]["use_drawdown"],
        reward_scale=config["env"]["reward_scale"]
    )
    
    return env, {"scaler": transforms.transforms[-1], "data_loader": data_loader}


def setup_model(config: Dict[str, Any], device: torch.device) -> Tuple[nn.Module, optim.Optimizer]:
    """Initialize the model and optimizer.
    
    Args:
        config: Configuration dictionary
        device: Device to run the model on
        
    Returns:
        Tuple containing model and optimizer
    """
    model = TCN_CNN_PPO(
        obs_dim=config["model"]["obs_dim"],
        action_dim=config["model"]["action_dim"],
        tcn_channels=config["model"]["tcn_channels"],
        cnn_filters=config["model"]["cnn_filters"],
        kernel_size=config["model"]["kernel_size"],
        dropout=config["model"]["dropout"]
    ).to(device)
    
    optimizer = optim.Adam(
        model.parameters(),
        lr=config["training"]["learning_rate"],
        eps=1e-5
    )
    
    return model, optimizer


def train(
    config: Dict[str, Any],
    model: nn.Module,
    optimizer: optim.Optimizer,
    env: CryptoTradingEnv,
    device: torch.device,
    run_dir: Path,
    checkpoint_interval: int = 10
) -> Dict[str, float]:
    """Train the model.
    
    Args:
        config: Configuration dictionary
        model: Model to train
        optimizer: Optimizer
        env: Trading environment
        device: Device to run training on
        run_dir: Directory to save checkpoints and logs
        checkpoint_interval: Save checkpoint every N episodes
        
    Returns:
        Dictionary of final metrics
    """
    # Create directories
    checkpoint_dir = run_dir / "checkpoints"
    checkpoint_dir.mkdir(exist_ok=True)
    
    # Initialize agent
    agent = PPOAgent(
        model=model,
        optimizer=optimizer,
        device=device,
        gamma=config["training"]["gamma"],
        epsilon=config["training"]["epsilon"],
        entropy_coef=config["training"]["entropy_coef"],
        ppo_epochs=config["training"]["ppo_epochs"],
        batch_size=config["training"]["batch_size"],
        clip_param=config["training"]["clip_param"]
    )
    
    # Initialize TensorBoard
    writer = SummaryWriter(log_dir=str(run_dir / "logs"))
    
    # Training loop
    best_sharpe = -np.inf
    metrics_history = []
    
    for episode in range(config["training"]["max_episodes"]):
        # Reset environment
        obs = env.reset()
        done = False
        episode_reward = 0
        episode_steps = 0
        
        while not done:
            # Get action from agent
            action, log_prob, value = agent.act(obs)
            
            # Take step in environment
            next_obs, reward, done, info = env.step(action)
            
            # Store transition
            agent.store_transition(
                state=obs,
                action=action,
                reward=reward,
                next_state=next_obs,
                done=done,
                log_prob=log_prob,
                value=value
            )
            
            # Update observation
            obs = next_obs
            episode_reward += reward
            episode_steps += 1
            
            # Update agent if we have enough samples
            if len(agent.buffer) >= config["training"]["batch_size"]:
                agent.update()
        
        # Log metrics
        metrics = {
            "episode": episode,
            "episode_reward": episode_reward,
            "episode_steps": episode_steps,
            "portfolio_value": info.get("portfolio_value", 0),
            "sharpe_ratio": info.get("sharpe_ratio", 0),
            "max_drawdown": info.get("max_drawdown_pct", 0) / 100,
            "win_rate": info.get("win_rate_pct", 0) / 100,
            "num_trades": info.get("num_trades", 0)
        }
        
        # Update best model
        if metrics["sharpe_ratio"] > best_sharpe:
            best_sharpe = metrics["sharpe_ratio"]
            save_checkpoint(
                model=model,
                optimizer=optimizer,
                episode=episode,
                metrics=metrics,
                is_best=True,
                checkpoint_dir=checkpoint_dir
            )
        
        # Save checkpoint
        if (episode + 1) % checkpoint_interval == 0:
            save_checkpoint(
                model=model,
                optimizer=optimizer,
                episode=episode,
                metrics=metrics,
                is_best=False,
                checkpoint_dir=checkpoint_dir
            )
        
        # Log to TensorBoard
        for key, value in metrics.items():
            if isinstance(value, (int, float)):
                writer.add_scalar(f"train/{key}", value, episode)
        
        # Print progress
        print(f"Episode {episode + 1}/{config['training']['max_episodes']} - "
              f"Reward: {episode_reward:.2f} - "
              f"Portfolio: ${metrics['portfolio_value']:,.2f} - "
              f"Sharpe: {metrics['sharpe_ratio']:.2f}")
        
        metrics_history.append(metrics)
    
    # Close TensorBoard writer
    writer.close()
    
    # Save final metrics
    with open(run_dir / "metrics.json", "w") as f:
        json.dump(metrics_history, f, indent=2)
    
    return metrics_history[-1] if metrics_history else {}


def save_checkpoint(
    model: nn.Module,
    optimizer: optim.Optimizer,
    episode: int,
    metrics: Dict[str, Any],
    is_best: bool,
    checkpoint_dir: Path
) -> None:
    """Save model checkpoint.
    
    Args:
        model: Model to save
        optimizer: Optimizer state
        episode: Current episode
        metrics: Training metrics
        is_best: Whether this is the best model so far
        checkpoint_dir: Directory to save checkpoints
    """
    checkpoint = {
        "episode": episode,
        "model_state_dict": model.state_dict(),
        "optimizer_state_dict": optimizer.state_dict(),
        "metrics": metrics
    }
    
    # Save checkpoint
    checkpoint_path = checkpoint_dir / f"checkpoint_ep{episode:04d}.pt"
    torch.save(checkpoint, checkpoint_path)
    
    # Save best model separately
    if is_best:
        best_path = checkpoint_dir / "best_model.pt"
        torch.save(checkpoint, best_path)
        
        # Save metrics
        with open(checkpoint_dir / "best_metrics.json", "w") as f:
            json.dump(metrics, f, indent=2)


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Train TCN-CNN-PPO trading model")
    parser.add_argument("--config", type=str, default="config/default.yaml",
                        help="Path to config file")
    parser.add_argument("--run-dir", type=str, default="runs/experiment",
                        help="Directory to save logs and checkpoints")
    parser.add_argument("--device", type=str, default=None,
                        help="Device to use (cpu, cuda, cuda:0, etc.)")
    return parser.parse_args()


def main():
    """Main training function."""
    # Parse arguments
    args = parse_args()
    
    # Load config
    config = load_config(args.config)
    
    # Set device
    device = args.device or ("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")
    
    # Create run directory
    run_dir = Path(args.run_dir) / datetime.now().strftime("%Y%m%d_%H%M%S")
    run_dir.mkdir(parents=True, exist_ok=True)
    
    # Save config
    save_config(config, run_dir / "config.yaml")
    
    # Set random seeds
    torch.manual_seed(config["training"].get("seed", 42))
    np.random.seed(config["training"].get("seed", 42))
    
    # Setup environment and model
    print("Setting up environment...")
    env, _ = setup_environment(config)
    
    print("Initializing model...")
    model, optimizer = setup_model(config, device)
    
    # Print model summary
    print(model)
    print(f"Trainable parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")
    
    # Train model
    print("Starting training...")
    metrics = train(
        config=config,
        model=model,
        optimizer=optimizer,
        env=env,
        device=device,
        run_dir=run_dir,
        checkpoint_interval=config["training"].get("checkpoint_interval", 10)
    )
    
    print("\nTraining completed!")
    print(f"Final metrics: {metrics}")
    print(f"Results saved to: {run_dir}")


if __name__ == "__main__":
    main()
