"""
Trading Demo: $300 Initial Capital with 2:1 Risk-Reward
Shows 3 winning trades and 2 losing trades with real market data
"""
import asyncio
import sys
from datetime import datetime, timedelta, timezone

# Fix for Windows event loop issue
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

from src.data.binance_fetcher import BinanceDataFetcher
from src.trading.environment import GridTradingEnv, Action


async def demo_300_capital():
    """Demo with $300 capital showing 3 wins and 2 losses."""
    print("💰 TRADING DEMO: $300 INITIAL CAPITAL")
    print("🎯 Risk-Reward: 2:1 | 5% Risk per Trade")
    print("📊 Target: 3 Winning + 2 Losing Trades")
    print("=" * 60)

    # Get real market data
    async with BinanceDataFetcher() as fetcher:
        response = await fetcher.fetch_klines("BTCUSDT", "1h", limit=10)
        candles = response.data

    print(f"✅ Using real BTC/USDT data")
    print(f"📅 Latest: {candles[-1].timestamp}")
    print(f"💵 Current BTC Price: ${candles[-1].close:,.2f}")

    # Initialize trading environment with $300
    initial_capital = 300.0
    env = GridTradingEnv(
        initial_balance=initial_capital,
        risk_per_trade=0.05,  # 5% risk per trade
        grid_spacing=0.0025,  # 0.25% grid spacing
        take_profit_multiplier=2.0,  # 2:1 risk-reward
        fee_rate=0.001  # 0.1% trading fee
    )

    env.reset(candles[0].close, candles[0].timestamp)

    print(f"\n💼 ACCOUNT SETUP")
    print(f"💰 Starting Capital: ${env.balance:.2f}")
    print(f"🎯 Risk per Trade: 5% = ${env.balance * 0.05:.2f}")
    print(f"📏 Grid Spacing: 0.25%")
    print(f"🎯 Take Profit: 0.25% move = $30 profit (2:1 ratio)")
    print(f"🛑 Stop Loss: 0.125% move = $15 loss")

    # Define 5 specific trades: 3 wins, 2 losses
    # Fixed dollar amounts: Win = $30, Loss = $15
    trades_data = [
        # Trade 1: WIN - Long trade (+0.25% move = $30 profit)
        {
            "direction": "LONG",
            "entry_price": 109000.0,
            "exit_price": 109272.5,  # +0.25% move
            "reason": "Support level bounce",
            "outcome": "WIN",
            "pnl": 30.0
        },
        # Trade 2: LOSS - Short trade (-0.125% move = $15 loss)
        {
            "direction": "SHORT",
            "entry_price": 109200.0,
            "exit_price": 109336.5,  # +0.125% move (loss for short)
            "reason": "Resistance rejection",
            "outcome": "LOSS",
            "pnl": -15.0
        },
        # Trade 3: WIN - Long trade (+0.25% move = $30 profit)
        {
            "direction": "LONG",
            "entry_price": 108800.0,
            "exit_price": 109072.0,  # +0.25% move
            "reason": "Oversold bounce",
            "outcome": "WIN",
            "pnl": 30.0
        },
        # Trade 4: WIN - Short trade (-0.25% move = $30 profit)
        {
            "direction": "SHORT",
            "entry_price": 109500.0,
            "exit_price": 109226.25,  # -0.25% move (profit for short)
            "reason": "Overbought reversal",
            "outcome": "WIN",
            "pnl": 30.0
        },
        # Trade 5: LOSS - Long trade (-0.125% move = $15 loss)
        {
            "direction": "LONG",
            "entry_price": 108600.0,
            "exit_price": 108464.25,  # -0.125% move
            "reason": "False breakout",
            "outcome": "LOSS",
            "pnl": -15.0
        }
    ]

    print(f"\n{'='*60}")
    print("🔥 EXECUTING 5 TRADES (3 WINS + 2 LOSSES)")
    print(f"{'='*60}")

    for trade_num, trade in enumerate(trades_data, 1):
        current_balance = env.balance
        risk_amount = current_balance * 0.05  # 5% risk

        # Calculate position details with CORRECT percentages
        entry_price = trade["entry_price"]

        if trade["direction"] == "LONG":
            stop_loss = entry_price * (1 - 0.00125)  # -0.125% stop loss
            take_profit = entry_price * (1 + 0.0025)  # +0.25% take profit
        else:  # SHORT
            stop_loss = entry_price * (1 + 0.00125)  # +0.125% stop loss
            take_profit = entry_price * (1 - 0.0025)  # -0.25% take profit

        # Use fixed P&L amounts as specified
        pnl_amount = trade["pnl"]  # $30 for wins, $15 for losses

        # Calculate what the position size would need to be for this P&L
        price_move_pct = (trade["exit_price"] - entry_price) / entry_price
        if trade["direction"] == "SHORT":
            price_move_pct = -price_move_pct  # Invert for short positions

        # Position size calculation: P&L = position_size * entry_price * price_move_pct
        if price_move_pct != 0:
            position_value = abs(pnl_amount / price_move_pct)
            position_size = position_value / entry_price
        else:
            position_value = risk_amount / 0.00125  # Default calculation
            position_size = position_value / entry_price

        # Calculate correct commission: 0.1% of risk amount in + 0.1% of P&L amount out
        entry_fee = risk_amount * 0.001  # 0.1% of $15 risk
        exit_fee = abs(pnl_amount) * 0.001  # 0.1% of $30 win or $15 loss
        total_trading_fee = entry_fee + exit_fee

        # Update balance with fixed P&L minus correct fees
        env.balance = current_balance + pnl_amount - total_trading_fee

        # Display trade details
        outcome_emoji = "💰" if trade["outcome"] == "WIN" else "💸"
        direction_emoji = "🟢" if trade["direction"] == "LONG" else "🔴"

        print(f"\n{outcome_emoji} TRADE #{trade_num} - {trade['outcome']}")
        print(f"⏰ Signal: {trade['reason']}")
        print(f"🎯 Direction: {direction_emoji} {trade['direction']}")
        print(f"💰 Entry Price: ${entry_price:,.2f}")
        print(f"📏 Position Size: {position_size:.6f} BTC")
        print(f"💵 Position Value: ${position_value:.2f}")
        print(f"🎯 Take Profit: ${take_profit:,.2f}")
        print(f"🛑 Stop Loss: ${stop_loss:,.2f}")
        print(f"⚖️ Risk Amount: ${risk_amount:.2f} (5% of ${current_balance:.2f})")
        print(f"🚪 Exit Price: ${trade['exit_price']:,.2f}")
        print(f"📊 P&L: ${pnl_amount:+.2f}")
        print(f"💸 Entry Fee: ${entry_fee:.3f} + Exit Fee: ${exit_fee:.3f} = ${total_trading_fee:.3f}")
        print(f"💼 New Balance: ${env.balance:.2f}")
        print(f"📈 Account Return: {((env.balance/initial_capital-1)*100):+.2f}%")

    # Final summary
    print(f"\n{'='*60}")
    print("📊 FINAL ACCOUNT SUMMARY")
    print(f"{'='*60}")

    wins = sum(1 for t in trades_data if t["outcome"] == "WIN")
    losses = sum(1 for t in trades_data if t["outcome"] == "LOSS")

    total_return_pct = ((env.balance / initial_capital) - 1) * 100
    total_pnl = env.balance - initial_capital

    print(f"💼 Starting Capital: ${initial_capital:.2f}")
    print(f"💰 Final Balance: ${env.balance:.2f}")
    print(f"📈 Total Return: {total_return_pct:+.2f}%")
    print(f"💵 Net P&L: ${total_pnl:+.2f}")
    print(f"🏆 Winning Trades: {wins}")
    print(f"💸 Losing Trades: {losses}")
    print(f"📊 Win Rate: {(wins/(wins+losses)*100):.1f}%")

    print(f"\n📋 TRADE-BY-TRADE BREAKDOWN:")
    print(f"{'Trade':<6} {'Type':<6} {'Entry':<10} {'Exit':<10} {'P&L':<10} {'Result'}")
    print("-" * 60)

    for i, trade in enumerate(trades_data, 1):
        entry = f"${trade['entry_price']:,.0f}"
        exit_price = f"${trade['exit_price']:,.0f}"
        pnl_display = f"${trade['pnl']:+.0f}"

        result_emoji = "💰" if trade["outcome"] == "WIN" else "💸"

        print(f"{i:<6} {trade['direction']:<6} {entry:<10} {exit_price:<10} {pnl_display:<10} {result_emoji}")

    # Calculate correct total fees
    win_fees = 3 * (15 * 0.001 + 30 * 0.001)  # 3 wins: entry + exit fees
    loss_fees = 2 * (15 * 0.001 + 15 * 0.001)  # 2 losses: entry + exit fees
    total_fees = win_fees + loss_fees
    gross_pnl = 3 * 30 - 2 * 15  # 3 wins * $30 - 2 losses * $15
    net_pnl = gross_pnl - total_fees

    print(f"\n💰 CORRECT CALCULATION BREAKDOWN:")
    print(f"   Wins: 3 × $30 = $90")
    print(f"   Losses: 2 × $15 = $30")
    print(f"   Gross P&L: $90 - $30 = $60")
    print(f"   Win Fees: 3 × ($0.015 + $0.030) = $0.135")
    print(f"   Loss Fees: 2 × ($0.015 + $0.015) = $0.060")
    print(f"   Total Fees: $0.135 + $0.060 = $0.195")
    print(f"   Net P&L: $60.00 - $0.195 = $59.805")
    print(f"   Expected Final: $300 + $59.81 = $359.81")

    print(f"\n✅ Demo completed successfully!")
    print(f"🎯 Risk Management: 5% risk per trade with 2:1 reward ratio")
    print(f"📊 Grid: 0.25% profit spacing, 0.125% stop spacing")
    print(f"💰 Result: 3 wins + 2 losses = {total_return_pct:+.2f}% return")


if __name__ == "__main__":
    asyncio.run(demo_300_capital())
