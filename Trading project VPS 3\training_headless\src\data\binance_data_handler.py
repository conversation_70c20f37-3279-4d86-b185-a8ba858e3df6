"""
Binance Data Handler for Grid Trading System
Handles data fetching, indicator calculation, and database management
"""
import os
import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Optional, Tuple, Dict, List
import time
import logging
from binance.client import Client
from binance.exceptions import BinanceAPIException

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BinanceDataHandler:
    def __init__(self, api_key: str, api_secret: str, db_path: str = 'trading_data.db'):
        """
        Initialize the Binance data handler
        
        Args:
            api_key: Binance API key
            api_secret: Binance API secret
            db_path: Path to SQLite database file
        """
        self.client = Client(api_key, api_secret)
        self.db_path = db_path
        self._init_database()
        
        # Trading parameters
        self.symbols = ['BTCUSDT', 'ETHUSDT']
        self.timeframe = '1h'
        self.cache = {}
        self.cache_ttl = 300  # 5 minutes cache TTL
        
        # Data split configuration (60 days training, 30 days testing)
        self.total_lookback_days = 90
        self.train_days = 60
        self.test_days = 30
        
    def _init_database(self):
        """Initialize the SQLite database with required tables"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # OHLCV data table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS ohlcv (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                timestamp DATETIME NOT NULL,
                open REAL NOT NULL,
                high REAL NOT NULL,
                low REAL NOT NULL,
                close REAL NOT NULL,
                volume REAL NOT NULL,
                UNIQUE(symbol, timestamp)
            )
            ''')
            
            # Indicators table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS indicators (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                timestamp DATETIME NOT NULL,
                vwap REAL,
                bb_upper REAL,
                bb_middle REAL,
                bb_lower REAL,
                rsi REAL,
                eth_btc_ratio REAL,
                UNIQUE(symbol, timestamp)
            )
            ''')
            
            # Create indexes for faster lookups
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_ohlcv_symbol ON ohlcv(symbol)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_ohlcv_timestamp ON ohlcv(timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_indicators_symbol ON indicators(symbol)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_indicators_timestamp ON indicators(timestamp)')
            
            conn.commit()
    
    def fetch_historical_data(self, symbol: str, start_time: Optional[datetime] = None, 
                            end_time: Optional[datetime] = None, limit: int = 1000) -> pd.DataFrame:
        """
        Fetch historical OHLCV data from Binance
        
        Args:
            symbol: Trading pair symbol (e.g., 'BTCUSDT')
            start_time: Start time for data fetch
            end_time: End time for data fetch
            limit: Maximum number of candles to fetch
            
        Returns:
            DataFrame with OHLCV data
        """
        try:
            # Convert datetime to milliseconds for Binance API
            start_ts = int(start_time.timestamp() * 1000) if start_time else None
            end_ts = int(end_time.timestamp() * 1000) if end_time else None
            
            # Fetch klines from Binance
            klines = self.client.get_klines(
                symbol=symbol,
                interval=Client.KLINE_INTERVAL_1HOUR,
                startTime=start_ts,
                endTime=end_ts,
                limit=limit
            )
            
            # Convert to DataFrame
            df = pd.DataFrame(klines, columns=[
                'open_time', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_volume', 'trades', 'taker_buy_base',
                'taker_buy_quote', 'ignore'
            ])
            
            # Convert columns to appropriate data types
            df['timestamp'] = pd.to_datetime(df['open_time'], unit='ms')
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = df[col].astype(float)
                
            df = df[['timestamp', 'open', 'high', 'low', 'close', 'volume']]
            df['symbol'] = symbol
            
            return df
            
        except BinanceAPIException as e:
            logger.error(f"Error fetching data for {symbol}: {e}")
            return pd.DataFrame()
    
    def update_ohlcv_data(self, symbol: str, lookback_days: int = 30) -> bool:
        """
        Update OHLCV data in the database
        
        Args:
            symbol: Trading pair symbol
            lookback_days: Number of days of historical data to fetch
            
        Returns:
            bool: True if update was successful
        """
        try:
            # Get the most recent timestamp from database
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT MAX(timestamp) FROM ohlcv WHERE symbol = ?
                ''', (symbol,))
                result = cursor.fetchone()
                
                if result and result[0]:
                    # Get data from last timestamp
                    last_timestamp = pd.to_datetime(result[0])
                    start_time = last_timestamp
                else:
                    # No data yet, fetch lookback_days
                    start_time = datetime.utcnow() - timedelta(days=lookback_days)
            
            # Fetch new data
            new_data = self.fetch_historical_data(
                symbol=symbol,
                start_time=start_time,
                end_time=datetime.utcnow()
            )
            
            if new_data.empty:
                logger.warning(f"No new data for {symbol}")
                return False
                
            # Store in database
            with sqlite3.connect(self.db_path) as conn:
                new_data.to_sql('ohlcv', conn, if_exists='append', index=False)
                
            logger.info(f"Updated OHLCV data for {symbol} with {len(new_data)} new candles")
            return True
            
        except Exception as e:
            logger.error(f"Error updating OHLCV data for {symbol}: {e}")
            return False
    
    def calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate technical indicators for the given OHLCV data
        
        Args:
            df: DataFrame with OHLCV data
            
        Returns:
            DataFrame with added indicator columns
        """
        if df.empty:
            return df
            
        # Make a copy to avoid modifying the original
        df = df.copy()
        
        # Calculate VWAP
        df['tp'] = (df['high'] + df['low'] + df['close']) / 3
        df['tp_vol'] = df['tp'] * df['volume']
        df['cum_tp_vol'] = df['tp_vol'].rolling(window=20, min_periods=1).sum()
        df['cum_vol'] = df['volume'].rolling(window=20, min_periods=1).sum()
        df['vwap'] = df['cum_tp_vol'] / df['cum_vol']
        
        # Calculate Bollinger Bands (20,2)
        df['bb_middle'] = df['close'].rolling(window=20).mean()
        df['bb_std'] = df['close'].rolling(window=20).std()
        df['bb_upper'] = df['bb_middle'] + (df['bb_std'] * 2)
        df['bb_lower'] = df['bb_middle'] - (df['bb_std'] * 2)
        
        # Calculate RSI (5)
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=5).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=5).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # Clean up temporary columns
        df.drop(['tp', 'tp_vol', 'cum_tp_vol', 'cum_vol', 'bb_std'], axis=1, inplace=True)
        
        return df
    
    def calculate_eth_btc_ratio(self, btc_data: pd.DataFrame, eth_data: pd.DataFrame) -> pd.Series:
        """
        Calculate ETH/BTC ratio
        
        Args:
            btc_data: DataFrame with BTC OHLCV data
            eth_data: DataFrame with ETH OHLCV data
            
        Returns:
            Series with ETH/BTC ratio
        """
        # Ensure both DataFrames have the same timestamps
        common_index = btc_data.index.intersection(eth_data.index)
        btc_close = btc_data.loc[common_index, 'close']
        eth_close = eth_data.loc[common_index, 'close']
        
        return eth_close / btc_close
    
    def update_indicators(self):
        """Update all indicators in the database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Get the most recent timestamp with indicators
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT MAX(timestamp) FROM indicators
                ''')
                result = cursor.fetchone()
                
                if result and result[0]:
                    last_indicator_time = pd.to_datetime(result[0])
                    start_time = last_indicator_time - timedelta(days=1)  # Some overlap
                else:
                    # No indicators yet, get all OHLCV data
                    start_time = None
                
                # Fetch OHLCV data for both symbols
                query = 'SELECT * FROM ohlcv WHERE timestamp >= ?' if start_time else 'SELECT * FROM ohlcv'
                params = (start_time.strftime('%Y-%m-%d %H:%M:%S'),) if start_time else ()
                
                btc_data = pd.read_sql(
                    query + ' AND symbol = "BTCUSDT" ORDER BY timestamp',
                    conn,
                    params=params,
                    parse_dates=['timestamp']
                )
                
                eth_data = pd.read_sql(
                    query + ' AND symbol = "ETHUSDT" ORDER BY timestamp',
                    conn,
                    params=params,
                    parse_dates=['timestamp']
                )
                
                if btc_data.empty or eth_data.empty:
                    logger.warning("Insufficient data for indicator calculation")
                    return False
                
                # Calculate indicators
                btc_data = self.calculate_indicators(btc_data)
                eth_data = self.calculate_indicators(eth_data)
                
                # Calculate ETH/BTC ratio
                eth_btc_ratio = self.calculate_eth_btc_ratio(btc_data, eth_data)
                btc_data['eth_btc_ratio'] = eth_btc_ratio
                eth_data['eth_btc_ratio'] = eth_btc_ratio
                
                # Prepare data for database insertion
                indicator_cols = ['symbol', 'timestamp', 'vwap', 'bb_upper', 
                                'bb_middle', 'bb_lower', 'rsi', 'eth_btc_ratio']
                
                btc_indicators = btc_data[btc_data['timestamp'] > last_indicator_time][indicator_cols] \
                    if 'last_indicator_time' in locals() else btc_data[indicator_cols]
                    
                eth_indicators = eth_data[eth_data['timestamp'] > last_indicator_time][indicator_cols] \
                    if 'last_indicator_time' in locals() else eth_data[indicator_cols]
                
                # Store indicators in database
                if not btc_indicators.empty:
                    btc_indicators.to_sql('indicators', conn, if_exists='append', index=False)
                    
                if not eth_indicators.empty:
                    eth_indicators.to_sql('indicators', conn, if_exists='append', index=False)
                
                logger.info(f"Updated indicators for {len(btc_indicators)} BTC and {len(eth_indicators)} ETH candles")
                return True
                
        except Exception as e:
            logger.error(f"Error updating indicators: {e}")
            return False
    
    def get_train_test_data(self, symbol: str = 'BTCUSDT') -> tuple[pd.DataFrame, pd.DataFrame]:
        """
        Get train and test datasets with a 60/30 day split
        
        Args:
            symbol: Trading pair symbol (default: 'BTCUSDT')
            
        Returns:
            tuple: (train_data, test_data) DataFrames
        """
        cache_key = f"{symbol}_train_test"
        current_time = time.time()
        
        # Check cache first
        if cache_key in self.cache:
            cached_data, timestamp = self.cache[cache_key]
            if current_time - timestamp < self.cache_ttl:
                return cached_data
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Get the most recent 90 days of data
                end_date = datetime.utcnow()
                start_date = end_date - timedelta(days=self.total_lookback_days)
                
                query = '''
                    SELECT o.*, i.vwap, i.bb_upper, i.bb_middle, i.bb_lower, i.rsi, i.eth_btc_ratio
                    FROM ohlcv o
                    LEFT JOIN indicators i ON o.symbol = i.symbol AND o.timestamp = i.timestamp
                    WHERE o.symbol = ? AND o.timestamp BETWEEN ? AND ?
                    ORDER BY o.timestamp
                '''
                
                # Fetch all data
                df = pd.read_sql(
                    query, 
                    conn, 
                    params=(symbol, start_date.strftime('%Y-%m-%d %H:%M:%S'), end_date.strftime('%Y-%m-%d %H:%M:%S')), 
                    parse_dates=['timestamp']
                )
                
                if df.empty:
                    raise ValueError(f"No data found for {symbol} in the specified date range")
                
                # Split into train (first 60 days) and test (last 30 days)
                split_date = df['timestamp'].max() - timedelta(days=self.test_days)
                train_data = df[df['timestamp'] <= split_date].copy()
                test_data = df[df['timestamp'] > split_date].copy()
                
                # Cache the result
                result = (train_data, test_data)
                self.cache[cache_key] = (result, current_time)
                
                return result
                
        except Exception as e:
            logger.error(f"Error getting train/test data: {e}")
            return pd.DataFrame(), pd.DataFrame()
    
    def get_market_data(self, symbol: str, lookback: int = 100, use_test_data: bool = False) -> pd.DataFrame:
        """
        Get market data with indicators from cache or database
        
        Args:
            symbol: Trading pair symbol
            lookback: Number of candles to return
            use_test_data: If True, returns test data (last 30 days), else returns training data (first 60 days)
            
        Returns:
            DataFrame with OHLCV and indicators
        """
        if lookback == 'all':
            train_data, test_data = self.get_train_test_data(symbol)
            return test_data if use_test_data else train_data
            
        cache_key = f"{symbol}_{lookback}_{'test' if use_test_data else 'train'}"
        current_time = time.time()
        
        # Check cache first
        if cache_key in self.cache:
            cached_data, timestamp = self.cache[cache_key]
            if current_time - timestamp < self.cache_ttl:
                return cached_data
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Get either test or training data
                if use_test_data:
                    # Last 30 days for testing
                    end_date = datetime.utcnow()
                    start_date = end_date - timedelta(days=self.test_days)
                else:
                    # 60 days before the test period for training
                    end_date = datetime.utcnow() - timedelta(days=self.test_days)
                    start_date = end_date - timedelta(days=self.train_days)
                
                query = '''
                    SELECT o.*, i.vwap, i.bb_upper, i.bb_middle, i.bb_lower, i.rsi, i.eth_btc_ratio
                    FROM ohlcv o
                    LEFT JOIN indicators i ON o.symbol = i.symbol AND o.timestamp = i.timestamp
                    WHERE o.symbol = ? AND o.timestamp BETWEEN ? AND ?
                    ORDER BY o.timestamp DESC
                    LIMIT ?
                '''
                
                df = pd.read_sql(
                    query, 
                    conn, 
                    params=(symbol, start_date.strftime('%Y-%m-%d %H:%M:%S'), 
                           end_date.strftime('%Y-%m-%d %H:%M:%S'), lookback), 
                    parse_dates=['timestamp']
                )
                
                # Cache the result
                self.cache[cache_key] = (df, current_time)
                
                return df
                
        except Exception as e:
            logger.error(f"Error getting market data for {symbol}: {e}")
            return pd.DataFrame()
    
    def update_all_data(self, lookback_days: int = 90):
        """
        Update all data and indicators
        
        Args:
            lookback_days: Number of days of historical data to fetch (default: 90)
        """
        logger.info(f"Starting data update (last {lookback_days} days)...")
        self.total_lookback_days = lookback_days
        
        # Update OHLCV data for all symbols
        for symbol in self.symbols:
            logger.info(f"Updating {symbol} data...")
            self.update_ohlcv_data(symbol, lookback_days=lookback_days)
            
        # Update indicators
        logger.info("Calculating indicators...")
        self.update_indicators()
        
        # Verify data split
        train_data, test_data = self.get_train_test_data()
        logger.info(f"Data update complete. Training data: {len(train_data)} candles, "
                   f"Test data: {len(test_data)} candles")


# Example usage
if __name__ == "__main__":
    # Initialize with your Binance API credentials
    api_key = os.getenv('BINANCE_API_KEY')
    api_secret = os.getenv('BINANCE_API_SECRET')
    
    if not api_key or not api_secret:
        raise ValueError("Please set BINANCE_API_KEY and BINANCE_API_SECRET environment variables")
    
    handler = BinanceDataHandler(api_key, api_secret)
    
    # Update all data
    handler.update_all_data()
    
    # Get latest market data
    btc_data = handler.get_market_data('BTCUSDT')
    print(f"Latest BTC data:\n{btc_data.tail()}")
