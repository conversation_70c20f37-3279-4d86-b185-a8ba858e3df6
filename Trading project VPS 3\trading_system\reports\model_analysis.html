
    <!DOCTYPE html>
    <html>
    <head>
        <title>TCN-CNN-PPO Model Analysis Report</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; color: #2c3e50; }
            .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
            .issue { background: #f8d7da; padding: 10px; margin: 5px 0; }
            .metric { background: #d4edda; padding: 10px; margin: 5px 0; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🔍 TCN-CNN-PPO Model Analysis Report</h1>
            <p>Generated: 1748262075.5415487</p>
        </div>

        <div class="section">
            <h2>📊 Action Distribution (100 Random Inputs)</h2>
            <div class="metric">BUY: 100% (100/100)</div>
            <div class="metric">SELL: 0% (0/100)</div>
            <div class="metric">HOLD: 0% (0/100)</div>
            <div class="metric">Average Confidence: 0.334</div>
        </div>

        <div class="section">
            <h2>🚨 Issues Identified</h2>
            <div class="issue">❌ Model has low confidence (may indicate poor training)</div>
        </div>

        <div class="section">
            <h2>🔧 Recommended Actions</h2>
            <ol>
                <li>Implement reward shaping to encourage profitable trading</li>
                <li>Add exploration mechanisms during training</li>
                <li>Use curriculum learning approach</li>
                <li>Enhance features with technical indicators</li>
                <li>Implement multi-objective training</li>
            </ol>
        </div>

        <div class="section">
            <h2>🎯 Next Steps</h2>
            <p>Based on this analysis, the model shows conservative behavior.
            The primary issue is likely in the reward function and training approach
            rather than the model architecture itself.</p>
        </div>
    </body>
    </html>
    